@extends('layouts.master')

@section('main_content')
    <div class="erp-table-section" style="padding: 40px 20px; background-color: #f9fbfc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; min-height: 100vh;">
        <div class="container-fluid" style="max-width: 900px; margin: auto;">
            <div class="cards" style="background: #fff; padding: 30px 35px; border-radius: 12px; box-shadow: 0 8px 16px rgba(0,0,0,0.05);">
                
                <h1 style="font-weight: 700; font-size: 2.4rem; color: #223344; margin-bottom: 30px;">Cutting Details</h1>
                
                <table style="width: 100%; border-collapse: separate; border-spacing: 0 10px; font-weight: 600; font-size: 1.1rem; color: #223344;">
                    <tbody>
                        @foreach ([
                            'ID' => $cutting->id,
                            'Production Date' => $cutting->production_date,
                            'Metre' => $cutting->metre,
                            'Lay' => $cutting->lay,
                            'Color' => ucfirst($cutting->color),
                            'Size 30' => $cutting->size_30,
                            'Size 32' => $cutting->size_32,
                            'Size 34' => $cutting->size_34,
                            'Size 36' => $cutting->size_36,
                            'Size 38' => $cutting->size_38,
                            'Size 40' => $cutting->size_40,
                            'Size 42' => $cutting->size_42,
                            'Size 44' => $cutting->size_44,
                            'Size 46' => $cutting->size_46,
                            'Size 48' => $cutting->size_48,
                            'Size 50' => $cutting->size_50,
                            'Total' => $cutting->total,
                            'Order ID' => $cutting->order_id,
                        ] as $label => $value)
                            <tr style="background: #e9f1f7; border-radius: 8px; box-shadow: 0 1px 3px rgba(33, 66, 99, 0.1);">
                                <th style="text-align: left; padding: 15px 20px; width: 35%; color: #0d3c78;">{{ $label }}</th>
                                <td style="padding: 15px 20px; border-left: 4px solid #0d3c78; border-radius: 0 8px 8px 0;">{{ $value }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                
                <a href="{{ route('cuttings.index') }}" 
                   style="
                        display: inline-block;
                        margin-top: 40px;
                        background-color: #0d3c78;
                        color: white;
                        font-weight: 700;
                        font-size: 1.15rem;
                        padding: 12px 32px;
                        border-radius: 30px;
                        text-decoration: none;
                        box-shadow: 0 4px 12px rgba(13, 60, 120, 0.4);
                        transition: background-color 0.3s ease;
                    "
                   onmouseover="this.style.backgroundColor='#0a2e5b'" 
                   onmouseout="this.style.backgroundColor='#0d3c78'">
                    ← Back to List
                </a>

            </div>
        </div>
    </div>
@endsection
