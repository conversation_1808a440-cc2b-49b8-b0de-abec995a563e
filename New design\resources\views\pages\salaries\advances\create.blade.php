@extends('layouts.master', [
    'title' => __('Create Advance Salary')
])

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.select2-container {
    width: 100% !important;
}
.select2-container--default .select2-selection--single {
    height: 40px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: #fff;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 38px;
    padding-left: 12px;
    color: #495057;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 38px;
    right: 10px;
}
.select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #007bff;
}
</style>
@endpush

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="table-header">
            <h4>{{ __('Create Advance Salary') }}</h4>
            <a href="{{ route('advance-salaries.index') }}" class="add-order-btn rounded-2">
                <i class="fas fa-arrow-left"></i> {{ __('Back to Advance List') }}
            </a>
        </div>

        <div class="order-form-section">
            <form action="{{ route('advance-salaries.store') }}" method="post" class="ajaxform">
                @csrf
                <div class="row">
                    <!-- Employee Selection -->
                    <div class="col-lg-6 mt-1">
                        <label>{{ __('Employee') }} <span class="text-danger">*</span></label>
                        <select name="employee_id" id="employee_id" class="form-control select2" required>
                            <option value="">{{ __('Select Employee') }}</option>
                            @foreach($employees as $employee)
                                <option value="{{ $employee->id }}" data-salary="{{ $employee->salary }}"
                                        data-employee-id="{{ $employee->employee_id }}">
                                    {{ $employee->employee_id }} - {{ $employee->name }} ({{ currency_format($employee->salary) }})
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Advance Amount -->
                    <div class="col-lg-6 mt-1">
                        <label>{{ __('Advance Amount') }} <span class="text-danger">*</span></label>
                        <input type="number" name="advance_amount" id="advance_amount" class="form-control" 
                               min="1" step="0.01" required placeholder="{{ __('Enter advance amount') }}">
                        <small class="form-text text-muted">{{ __('Cannot exceed base salary') }}</small>
                    </div>

                    <!-- Salary Period -->
                    <div class="col-lg-6 mt-1">
                        <label>{{ __('Year') }} <span class="text-danger">*</span></label>
                        <select name="year" id="year" class="form-control" required>
                            @for($year = date('Y'); $year >= date('Y') - 1; $year--)
                                <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                    {{ $year }}
                                </option>
                            @endfor
                        </select>
                    </div>

                    <div class="col-lg-6 mt-1">
                        <label>{{ __('Month') }} <span class="text-danger">*</span></label>
                        <select name="month" id="month" class="form-control" required>
                            @for($month = 1; $month <= 12; $month++)
                                <option value="{{ $month }}" {{ $month == date('n') ? 'selected' : '' }}>
                                    {{ \Carbon\Carbon::create()->month($month)->format('F') }}
                                </option>
                            @endfor
                        </select>
                    </div>

                    <!-- Employee Info Display -->
                    <div class="col-lg-12 mt-3" id="employee-info" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6>{{ __('Employee Information') }}</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <p><strong>{{ __('Employee ID') }}:</strong> <span id="display-employee-id">-</span></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p><strong>{{ __('Base Salary') }}:</strong> <span id="display-base-salary">-</span></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p><strong>{{ __('Max Advance') }}:</strong> <span id="display-max-advance">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Payment Date') }} <span class="text-danger">*</span></label>
                        <input type="date" name="payment_date" id="payment_date" class="form-control" 
                               value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}" required>
                    </div>

                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Payment Bank') }}</label>
                        <select name="bank_id" id="bank_id" class="form-control">
                            <option value="">{{ __('Cash Payment') }}</option>
                            @foreach($banks as $bank)
                                <option value="{{ $bank->id }}">{{ $bank->bank_name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Notes') }}</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="{{ __('Reason for advance payment') }}"></textarea>
                    </div>

                    <!-- Remarks -->
                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Remarks') }}</label>
                        <textarea name="remarks" id="remarks" class="form-control" rows="3" 
                                  placeholder="{{ __('Internal remarks or comments') }}"></textarea>
                    </div>

                    <!-- Warning Notice -->
                    <div class="col-lg-12 mt-3">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> {{ __('Important Notice') }}</h6>
                            <ul class="mb-0">
                                <li>{{ __('This advance will be deducted from the final salary for this period.') }}</li>
                                <li>{{ __('You can convert this advance to regular salary later by adding attendance and overtime data.') }}</li>
                                <li>{{ __('Advance amount cannot exceed the employee\'s base salary.') }}</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-lg-12 mt-4">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-hand-holding-usd"></i> {{ __('Create Advance Salary') }}
                        </button>
                        <a href="{{ route('advance-salaries.index') }}" class="btn btn-secondary">
                            {{ __('Cancel') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for employee dropdown
    $('#employee_id').select2({
        placeholder: '{{ __("Search and select employee...") }}',
        allowClear: true,
        width: '100%'
    });

    let currentBaseSalary = 0;

    // Employee selection change
    $('#employee_id').change(function() {
        const employeeId = $(this).val();
        const selectedOption = $(this).find('option:selected');
        
        if (employeeId) {
            const baseSalary = parseFloat(selectedOption.data('salary')) || 0;
            const employeeIdText = selectedOption.data('employee-id');
            
            currentBaseSalary = baseSalary;
            
            // Show employee info
            $('#employee-info').show();
            $('#display-employee-id').text(employeeIdText);
            $('#display-base-salary').text(formatCurrency(baseSalary));
            $('#display-max-advance').text(formatCurrency(baseSalary));
            
            // Set max value for advance amount
            $('#advance_amount').attr('max', baseSalary);
            
        } else {
            $('#employee-info').hide();
            $('#advance_amount').removeAttr('max');
            currentBaseSalary = 0;
        }
    });

    // Advance amount validation
    $('#advance_amount').on('input', function() {
        const advanceAmount = parseFloat($(this).val()) || 0;
        
        if (currentBaseSalary > 0 && advanceAmount > currentBaseSalary) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">{{ __("Advance amount cannot exceed base salary") }}</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    }
});
</script>
@endpush
