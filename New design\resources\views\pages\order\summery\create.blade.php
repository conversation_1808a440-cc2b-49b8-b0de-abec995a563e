<div style="padding:20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; max-width:100%; margin:auto; background:#f9f9f9; border-radius:12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
  <div style="border-bottom:1px solid #ddd; padding-bottom:10px; margin-bottom:20px;">
    <h4 style="font-weight:600; color:#333; margin:0;">{{__('Add New order')}}</h4>
  </div>
  <form action="{{ route('orders.store') }}" method="post" enctype="multipart/form-data" class="ajaxform_instant_reload" style="display:flex; flex-direction:column; gap:20px;">
    @csrf

    <!-- Image Upload -->
    <div style="display:flex; justify-content:center; margin-bottom:20px;">
      <label style="display:flex; flex-direction:column; align-items:center; cursor:pointer; border:2px dashed #ccc; border-radius:16px; width:140px; height:140px; justify-content:center; background:#fff; transition: border-color 0.3s;">
        <input type="file" name="image" id="upload" accept="image/*" class="d-none file-input-change" data-id="image" style="display:none;">
        <img src="{{ asset('assets/images/icons/upload.png') }}" id="image" class="img-preview" alt="Upload Icon" style="width:48px; height:48px; opacity:0.5; margin-bottom:8px;">
        <span style="font-size:14px; color:#666; font-weight:500;">{{__('Upload Item Image')}}</span>
      </label>
    </div>

    <!-- Party and Merchandiser Row -->
    <div style="display:flex; gap:12px; flex-wrap:wrap;">
      <div style="flex:1 1 48%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Party name')}}</label>
        <div style="display:flex; border:1px solid #ddd; border-radius:10px; overflow:hidden; margin-top:6px;">
          <select name="party_id" required style="flex-grow:1; border:none; outline:none; padding:10px 12px; font-size:15px; border-radius:10px 0 0 10px; background:#fff; color:#333;">
            <option value="">{{__('Select a Party')}}</option>
            @foreach($parties as $party)
              <option value="{{ $party->id }}">{{ $party->name }} ({{ optional($party->user)->phone }})</option>
            @endforeach
          </select>
          <a href="{{ route('parties.create') }}" style="display:flex; align-items:center; justify-content:center; padding:0 12px; background:#007aff; color:#fff; font-weight:600; border-radius:0 10px 10px 0; text-decoration:none;">
            <i class="fal fa-plus" style="font-style:normal;">＋</i>
          </a>
        </div>
      </div>

      <div style="flex:1 1 48%; {{ auth()->user()->role == 'merchandiser' ? 'display:none;' : '' }}">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Merchandiser name')}}</label>
        <div style="display:flex; border:1px solid #ddd; border-radius:10px; overflow:hidden; margin-top:6px;">
          <select name="merchandiser_id" required style="flex-grow:1; border:none; outline:none; padding:10px 12px; font-size:15px; border-radius:10px 0 0 10px; background:#fff; color:#333;">
            <option value="">{{__('Select a Merchandiser')}}</option>
            @foreach($merchandisers as $merchandiser)
              <option value="{{ $merchandiser->id }}">{{ $merchandiser->name }}</option>
            @endforeach
          </select>
          <a href="{{ route('users.create', ['users' => 'merchandiser']) }}" style="display:flex; align-items:center; justify-content:center; padding:0 12px; background:#007aff; color:#fff; font-weight:600; border-radius:0 10px 10px 0; text-decoration:none;">
            <i class="fal fa-plus" style="font-style:normal;">＋</i>
          </a>
        </div>
      </div>
    </div>

    <!-- Order No. and Title -->
    <div style="display:flex; gap:12px; flex-wrap:wrap;">
      <div style="flex:1 1 48%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Order No.')}}</label>
        <input type="text" name="order_no" required value="{{ $order_no ?? '' }}" placeholder="Enter Order No." style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
      <div style="flex:1 1 48%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Order Title')}}</label>
        <input type="text" name="title" required placeholder="Enter Order Title" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
    </div>

    <!-- Order Description and Fabrication -->
    <div style="display:flex; gap:12px; flex-wrap:wrap;">
      <div style="flex:1 1 48%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Order Description')}}</label>
        <input type="text" name="description" placeholder="Enter Order Description" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
      <div style="flex:1 1 48%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Fabrication')}}</label>
        <input type="text" name="fabrication" placeholder="Enter Fab Name" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
    </div>

    <!-- GSM, Yarn Count, Shipment Mode -->
    <div style="display:flex; gap:12px; flex-wrap:wrap; margin-top:10px;">
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('GSM')}}</label>
        <input type="text" name="gsm" placeholder="Enter GSM" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Yarn Count')}}</label>
        <input type="text" name="yarn_count" placeholder="Enter Yarn Count" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Shipment Mode')}}</label>
        <input type="text" name="shipment_mode" placeholder="Enter Shipment Mode" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
    </div>

    <!-- Payment Mode, Bank Account, Year -->
    <div style="display:flex; gap:12px; flex-wrap:wrap; margin-top:10px;">
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Payment Mode')}}</label>
        <input type="text" name="payment_mode" placeholder="Enter Payment Mode" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>

      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Bank Account')}}</label>
        <div style="display:flex; border:1px solid #ddd; border-radius:10px; overflow:hidden; margin-top:6px;">
          <select name="bank_id" required style="flex-grow:1; border:none; outline:none; padding:10px 12px; font-size:15px; border-radius:10px 0 0 10px; background:#fff; color:#333;">
            <option value="">{{__('Select a Account')}}</option>
            @foreach($banks as $bank)
              <option value="{{ $bank->id }}">{{ $bank->holder_name }} ({{ $bank->account_number  }})</option>
            @endforeach
          </select>
          <a href="{{ route('banks.index') }}" style="display:flex; align-items:center; justify-content:center; padding:0 12px; background:#007aff; color:#fff; font-weight:600; border-radius:0 10px 10px 0; text-decoration:none;">
            <i class="fal fa-plus" style="font-style:normal;">＋</i>
          </a>
        </div>
      </div>

      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Year')}}</label>
        <input type="text" name="year" placeholder="Enter Year" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
    </div>

    <!-- Season, Pantone, Consignee & Notify -->
    <div style="display:flex; gap:12px; flex-wrap:wrap; margin-top:10px;">
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Season')}}</label>
        <input type="text" name="season" placeholder="Enter Season" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>

      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Pantone')}}</label>
        <input type="text" name="meta[pantone]" placeholder="Enter Pantone" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>

      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Consignee & Notify')}}</label>
        <input type="text" name="invoice_details[consignee]" placeholder="Consignee & Notify" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
    </div>

    <!-- Contact Date, Expiry Date, Negotiation Period -->
    <div style="display:flex; gap:12px; flex-wrap:wrap; margin-top:10px;">
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Contact Date')}}</label>
        <input type="date" name="invoice_details[contact_date]" value="{{ now()->format('Y-m-d') }}" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>

      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Expiry Date')}}</label>
        <input type="date" name="invoice_details[expire_date]" value="{{ now()->format('Y-m-d') }}" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>

      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Negotiation Period')}}</label>
        <input type="text" name="invoice_details[negotiation]" placeholder="15 Days" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
    </div>

    <!-- Port of Loading, Port of Discharge, Payment Terms -->
    <div style="display:flex; gap:12px; flex-wrap:wrap; margin-top:10px;">
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Port of Loading')}}</label>
        <input type="text" name="invoice_details[loading]" placeholder="Loading Port" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Port of Discharge')}}</label>
        <input type="text" name="invoice_details[discharge]" placeholder="Discharge Port" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
      <div style="flex:1 1 30%;">
        <label style="font-weight:600; font-size:14px; color:#444;">{{__('Payment Terms')}}</label>
        <input type="text" name="invoice_details[payment_terms]" placeholder="Payment Terms" style="width:100%; padding:10px 12px; font-size:15px; border:1px solid #ddd; border-radius:12px; outline:none; margin-top:6px; background:#fff; color:#333;">
      </div>
    </div>

    <div class="col-lg-12 table-form-section">
                <div class="table-responsive responsive-table mt-4 pb-3">
                    <table class="table table-two daily-production-table-print mw-1000" id="erp-table">
                        <thead>
                            <tr>
                                <th><strong>{{__('STYLE')}}</strong></th>
                                <th><strong>{{__('COLOR')}}</strong></th>
                                <th><strong>{{__('CODE')}}</strong></th>

                                <th><strong>{{__('ITEM')}}</strong></th>
                                <th><strong>{{__('SHIPMENT DATE')}}</strong></th>
                                <th><strong>{{__('QTY')}}</strong></th>
                                <th><strong>{{__('UNIT PRICE')}}</strong></th>
                                <th><strong>{{__('TTL PRICE')}}</strong></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="position-relative">
                                <td>
                                    <div class="add-btn-one"><i class="fal fa-plus" aria-hidden="true"></i></div>
                                </td>
                                <td>
                                    <div class="tr-remove-btn remove-one"><i class="fas fa-trash"></i></div>
                                </td>
                            </tr>
                            <tr class="duplicate-one">
                                <td><input type="text" name="style[]" class="form-control style clear-input" required
                                        placeholder="Style"></td>
                                <td><input type="text" name="color[]" class="form-control color clear-input"
                                        placeholder="Color"></td>
                                <td><input type="text" name="code[]" class="form-control color clear-input"
                                        placeholder="Code"></td>

                                <td><input type="text" name="item[]" class="form-control item clear-input"
                                        placeholder="Item description"></td>
                                <td><input type="date" name="shipment_date[]" value="{{ now()->format('Y-m-d') }}"
                                        required class="form-control shipment_date clear-input"></td>
                                <td><input type="number" name="qty[]" class="form-control count-length qty 0"
                                        data-length="0" required placeholder="Qty"></td>
                                <td><input type="number" name="unit_price[]"
                                        class="form-control count-length unit_price 0" data-length="0" required
                                        placeholder="Unit price"></td>
                                <td><input type="number" name="total_price[]" class="form-control total_price 0"
                                        placeholder="Total price" readonly data-length="0" value="0"></td>
                            </tr>
                            <tr class="total">
                                <td colspan="4">
                                    <h6 class="text-end">Total</h6>
                                </td>
                                <td>
                                    <h6 class="total_qty">0</h6>
                                </td>
                                <td>
                                    <h6 class="total_unit_price">0</h6>
                                </td>
                                <td>
                                    <h6 class="final_total_price">0</h6>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
    <!-- Submit Button -->
    <div style="margin-top:30px; text-align:center;">
      <button type="submit" style="background:#007aff; color:#fff; font-weight:700; border:none; padding:14px 40px; font-size:16px; border-radius:30px; cursor:pointer; box-shadow: 0 4px 12px rgba(0,122,255,0.4); transition: background 0.3s;">
        {{__('Submit Order')}}
      </button>
    </div>
  </form>
</div>
