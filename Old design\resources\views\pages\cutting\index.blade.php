@extends('layouts.master')

@section('main_content')
    <div style="padding: 40px 20px; background-color: #f8f9fa; min-height: 100vh; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; color: #1e293b;">
        <div style="max-width: 1100px; margin: 0 auto;">
            <div style="background: #fff; border-radius: 16px; box-shadow: 0 4px 12px rgba(0,0,0,0.05); padding: 32px;">
                <div style="display: flex; justify-content: flex-end; margin-bottom: 24px;">
                    <a href="{{ route('cuttings.create') }}" 
                       style="background-color: #22c55e; color: white; padding: 10px 22px; border-radius: 9999px; font-weight: 600; text-decoration: none; box-shadow: 0 3px 8px rgba(34, 197, 94, 0.4); transition: background-color 0.3s ease;">
                        <i class="fas fa-plus" style="margin-right: 6px;"></i> Create New Cutting
                    </a>
                </div>

                @if ($message = Session::get('success'))
                    <div style="background-color: #dcfce7; color: #166534; padding: 12px 20px; border-radius: 8px; margin-bottom: 24px; box-shadow: inset 0 0 6px rgba(22,101,52,0.2); font-weight: 600;">
                        {{ $message }}
                    </div>
                @endif

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; text-align: center; font-size: 14px; color: #334155;">
                        <thead style="background-color: #e2e8f0; color: #475569; text-transform: uppercase; font-weight: 700;">
                            <tr>
                                <th style="padding: 14px 12px; border-bottom: 2px solid #cbd5e1;">ID</th>
                                <th style="padding: 14px 12px; border-bottom: 2px solid #cbd5e1;">Production Date</th>
                                <th style="padding: 14px 12px; border-bottom: 2px solid #cbd5e1;">Metre</th>
                                <th style="padding: 14px 12px; border-bottom: 2px solid #cbd5e1;">Lay</th>
                                <th style="padding: 14px 12px; border-bottom: 2px solid #cbd5e1;">Color</th>
                                <th style="padding: 14px 12px; border-bottom: 2px solid #cbd5e1;">Total QTY</th>
                                <th style="padding: 14px 12px; border-bottom: 2px solid #cbd5e1;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($cuttings as $cutting)
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="padding: 14px 12px;">{{ $cutting->id }}</td>
                                    <td style="padding: 14px 12px;">{{ \Carbon\Carbon::parse($cutting->production_date)->format('d M Y') }}</td>
                                    <td style="padding: 14px 12px;">{{ $cutting->metre }}</td>
                                    <td style="padding: 14px 12px;">{{ $cutting->lay }}</td>
                                    <td style="padding: 14px 12px;">{{ ucfirst($cutting->color) }}</td>
                                    <td style="padding: 14px 12px;">{{ $cutting->total }}</td>
                                    <td style="padding: 14px 12px;">
                                        <div style="display: flex; justify-content: center; gap: 8px; flex-wrap: wrap;">
                                            <a href="{{ route('cuttings.show', $cutting->id) }}"
                                               style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 9999px; font-size: 13px; text-decoration: none; box-shadow: 0 2px 6px rgba(59,130,246,0.4);">
                                                <i class="fas fa-eye"></i> Show
                                            </a>
                                            <a href="{{ route('cuttings.edit', $cutting->id) }}"
                                               style="background-color: #2563eb; color: white; padding: 6px 12px; border-radius: 9999px; font-size: 13px; text-decoration: none; box-shadow: 0 2px 6px rgba(37,99,235,0.4);">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <form action="{{ route('cuttings.destroy', $cutting->id) }}" method="POST" onsubmit="return confirm('Are you sure?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    style="background-color: #ef4444; color: white; padding: 6px 12px; border: none; border-radius: 9999px; font-size: 13px; cursor: pointer; box-shadow: 0 2px 6px rgba(239,68,68,0.4);">
                                                    <i class="fas fa-trash-alt"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 32px; display: flex; justify-content: center;">
                    {{ $cuttings->links() }}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
@endpush
