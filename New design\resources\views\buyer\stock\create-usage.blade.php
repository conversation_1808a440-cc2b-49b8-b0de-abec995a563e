@extends('layouts.master', [
    'title' => __('Record Stock Usage')
])

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4>{{ __('Record Stock Usage') }} - {{ $party->name }}</h4>
                <div class="header-action">
                    <a href="{{ route('buyer.stock.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ __('Back to Dashboard') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form id="usage-form" class="ajaxform_instant_reload">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="style">{{ __('Style') }} <span class="text-danger">*</span></label>
                                <select name="style" id="style" class="form-control" required>
                                    <option value="">{{ __('Select Style') }}</option>
                                    @foreach($availableStyles as $style)
                                        <option value="{{ $style }}">{{ $style }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="color">{{ __('Color') }}</label>
                                <select name="color" id="color" class="form-control">
                                    <option value="">{{ __('Select Color') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="item">{{ __('Item') }}</label>
                                <select name="item" id="item" class="form-control">
                                    <option value="">{{ __('Select Item') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="size">{{ __('Size') }}</label>
                                <select name="size" id="size" class="form-control">
                                    <option value="">{{ __('Select Size') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="qty_used">{{ __('Quantity to Use') }} <span class="text-danger">*</span></label>
                                <input type="number" name="qty_used" id="qty_used" class="form-control" min="1" required>
                                <small class="form-text text-muted">
                                    {{ __('Available Stock') }}: <span id="available-stock" class="font-weight-bold">-</span>
                                </small>
                                <div id="stock-warning" class="alert alert-warning mt-2" style="display: none;">
                                    <i class="fas fa-exclamation-triangle"></i> {{ __('This item is out of stock or has low stock.') }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="usage_date">{{ __('Usage Date') }} <span class="text-danger">*</span></label>
                                <input type="date" name="usage_date" id="usage_date" class="form-control" 
                                       value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="purpose">{{ __('Purpose/Reason') }}</label>
                                <input type="text" name="purpose" id="purpose" class="form-control" 
                                       placeholder="{{ __('e.g., Production, Sample, Quality Check') }}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="remarks">{{ __('Remarks') }}</label>
                                <textarea name="remarks" id="remarks" class="form-control" rows="3" 
                                          placeholder="{{ __('Additional notes or comments') }}"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ __('Record Usage') }}
                        </button>
                        <a href="{{ route('buyer.stock.index') }}" class="btn btn-secondary">
                            {{ __('Cancel') }}
                        </a>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // AJAX-based dropdown updates
    $('#style').change(function() {
        const selectedStyle = $(this).val();
        console.log('Style changed to:', selectedStyle);

        // Clear dependent dropdowns
        $('#color, #item, #size').empty().append('<option value="">{{ __("Select") }}</option>');
        $('#available-stock').text('-').removeClass('text-success text-warning text-danger');
        $('#stock-warning').hide();
        $('#qty_used').prop('disabled', false).removeAttr('max');

        if (selectedStyle) {
            loadDropdownOptions();
        }
    });

    // Update item and size when color changes
    $('#color').change(function() {
        console.log('Color changed to:', $(this).val());

        // Clear dependent dropdowns
        $('#item, #size').empty().append('<option value="">{{ __("Select") }}</option>');
        $('#available-stock').text('-').removeClass('text-success text-warning text-danger');
        $('#stock-warning').hide();

        loadDropdownOptions();
    });

    // Update size when item changes
    $('#item').change(function() {
        console.log('Item changed to:', $(this).val());

        // Clear size dropdown
        $('#size').empty().append('<option value="">{{ __("Select") }}</option>');
        $('#available-stock').text('-').removeClass('text-success text-warning text-danger');
        $('#stock-warning').hide();

        loadDropdownOptions();
    });

    // Update available stock when size changes
    $('#size').change(function() {
        console.log('Size changed to:', $(this).val());
        loadDropdownOptions();
    });

    // Validate quantity on input
    $('#qty_used').on('input', function() {
        const availableStock = parseInt($('#available-stock').text()) || 0;
        const requestedQty = parseInt($(this).val()) || 0;

        if (requestedQty > availableStock) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after('<div class="invalid-feedback">{{ __("Requested quantity exceeds available stock") }}</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // AJAX function to load dropdown options
    function loadDropdownOptions() {
        const style = $('#style').val();
        const color = $('#color').val();
        const item = $('#item').val();
        const size = $('#size').val();

        if (!style) return;

        console.log('Loading options for:', {style, color, item, size});

        $.ajax({
            url: '{{ route("buyer.stock.dropdown-options") }}',
            method: 'GET',
            data: {
                style: style,
                color: color,
                item: item,
                size: size
            },
            success: function(response) {
                console.log('AJAX Response:', response);

                // Update color dropdown if not selected
                if (!color) {
                    updateDropdownFromArray('#color', response.colors, 'Color');
                }

                // Update item dropdown if color is selected but item is not
                if (color && !item) {
                    updateDropdownFromArray('#item', response.items, 'Item');
                }

                // Update size dropdown if item is selected but size is not
                if (item && !size) {
                    updateDropdownFromArray('#size', response.sizes, 'Size');
                }

                // Update available stock
                updateStockDisplay(response.available_stock);
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                toastr.error('{{ __("Error loading dropdown options") }}');
            }
        });
    }

    function updateDropdownFromArray(selector, options, fieldName) {
        const dropdown = $(selector);
        dropdown.empty().append(`<option value="">{{ __("Select") }} ${fieldName}</option>`);

        options.forEach(function(value) {
            const displayValue = value === '' || value === null ? '{{ __("None") }}' : value;
            const optionValue = value === null ? '' : value;
            dropdown.append(`<option value="${optionValue}">${displayValue}</option>`);
        });

        console.log(`Updated ${fieldName} dropdown with:`, options);
    }

    function updateStockDisplay(availableStock) {
        console.log('Updating stock display with:', availableStock);

        $('#available-stock').text(availableStock);

        // Update color coding and warnings
        if (availableStock <= 0) {
            $('#available-stock').removeClass('text-success text-warning').addClass('text-danger');
            $('#stock-warning').show().html('<i class="fas fa-exclamation-triangle"></i> {{ __("This item is out of stock.") }}');
            $('#qty_used').prop('disabled', true).val('');
        } else if (availableStock <= 10) {
            $('#available-stock').removeClass('text-success text-danger').addClass('text-warning');
            $('#stock-warning').show().html('<i class="fas fa-exclamation-circle"></i> {{ __("Low stock warning: Only") }} ' + availableStock + ' {{ __("items available.") }}');
            $('#qty_used').prop('disabled', false);
        } else {
            $('#available-stock').removeClass('text-danger text-warning').addClass('text-success');
            $('#stock-warning').hide();
            $('#qty_used').prop('disabled', false);
        }

        // Update max attribute for quantity input
        $('#qty_used').attr('max', availableStock);
    }

    // Form submission
    $('#usage-form').submit(function(e) {
        e.preventDefault();
        
        const availableStock = parseInt($('#available-stock').text()) || 0;
        const requestedQty = parseInt($('#qty_used').val()) || 0;
        
        if (requestedQty > availableStock) {
            toastr.error('{{ __("Requested quantity exceeds available stock") }}');
            return;
        }
        
        $.ajax({
            url: '{{ route("buyer.stock.store-usage") }}',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                toastr.success(response.message);
                if (response.redirect) {
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response.message || '{{ __("An error occurred") }}');
            }
        });
    });
});
</script>
@endpush
