@extends('layouts.master')

@section('main_content')
    <div style="padding: 40px 0; background-color: #f8f9fa; min-height: 100vh;">
        <div class="container-fluid">
            <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                <div style="padding: 32px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; margin-bottom: 24px;">
                        <h2 style="margin: 0; font-weight: 600; color: #212529;">Inventory Items</h2>
                        <a href="{{ route('items.create') }}" style="padding: 8px 20px; background-color: #0d6efd; color: white; text-decoration: none; border-radius: 999px; font-size: 14px; margin-top: 10px;">
                            + Add Item
                        </a>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; text-align: center; background-color: #ffffff;">
                            <thead style="background-color: #f1f3f5; color: #6c757d;">
                                <tr>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">ID</th>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">Date</th>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">Color</th>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">QTY</th>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">Part</th>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">Size</th>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">Type</th>
                                    <th style="padding: 12px; font-size: 13px; text-transform: uppercase;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($items as $item)
                                    @foreach ($item->itemSizes as $size)
                                        <tr style="border-top: 1px solid #dee2e6;">
                                            <td style="padding: 12px; color: #6c757d;">{{ $item->id }}</td>
                                            <td style="padding: 12px;">{{ \Carbon\Carbon::parse($item->date)->format('d M Y') }}</td>
                                            <td style="padding: 12px;">{{ $size->color }}</td>
                                            <td style="padding: 12px;">{{ $size->qty }}</td>
                                            <td style="padding: 12px;">{{ $size->part }}</td>
                                            <td style="padding: 12px;">{{ $size->size }}</td>
                                            <td style="padding: 12px;">{{ $size->type }}</td>
                                            <td style="padding: 12px;">
                                                <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 8px;">
                                                    <a href="{{ route('items.show', $item->id) }}"
                                                       style="padding: 6px 12px; background-color: #0dcaf0; color: white; font-size: 13px; border-radius: 999px; text-decoration: none;">
                                                        View
                                                    </a>
                                                    <a href="{{ route('items.edit', $item->id) }}"
                                                       style="padding: 6px 12px; background-color: #0d6efd; color: white; font-size: 13px; border-radius: 999px; text-decoration: none;">
                                                        Edit
                                                    </a>
                                                    <form action="{{ route('items.destroy', $item->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this item?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                                style="padding: 6px 12px; background-color: #dc3545; color: white; font-size: 13px; border: none; border-radius: 999px;">
                                                            Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div style="margin-top: 32px; display: flex; justify-content: center;">
                        {{ $items->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
