@extends('layouts.master')

@section('main_content')
    <div class="erp-table-section">
        <div class="container-fluid">
            <div class="cards">
                <h1>Edit Cutting</h1>
                <form action="{{ route('cuttings.update', $cutting->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="production_date" class="form-label">Production Date:</label>
                                <input type="date" name="production_date" id="production_date" class="form-control"
                                    value="{{ $cutting->production_date }}">
                            </div>
                            <div class="mb-3">
                                <label for="metre" class="form-label">Metre:</label>
                                <input type="number" name="metre" id="metre" class="form-control"
                                    value="{{ $cutting->metre }}">
                            </div>
                            <div class="mb-3">
                                <label for="lay" class="form-label">Lay:</label>
                                <input type="number" name="lay" id="lay" class="form-control"
                                    value="{{ $cutting->lay }}">
                            </div>
                            <div class="mb-3">
                                <label for="color" class="form-label">Color:</label>
                                <input type="text" name="color" id="color" class="form-control"
                                    value="{{ $cutting->color }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_30" class="form-label">Size 30:</label>
                                <input type="number" name="size_30" id="size_30" class="form-control"
                                    value="{{ $cutting->size_30 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_32" class="form-label">Size 32:</label>
                                <input type="number" name="size_32" id="size_32" class="form-control"
                                    value="{{ $cutting->size_32 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_34" class="form-label">Size 34:</label>
                                <input type="number" name="size_34" id="size_34" class="form-control"
                                    value="{{ $cutting->size_34 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_36" class="form-label">Size 36:</label>
                                <input type="number" name="size_36" id="size_36" class="form-control"
                                    value="{{ $cutting->size_36 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_38" class="form-label">Size 38:</label>
                                <input type="number" name="size_38" id="size_38" class="form-control"
                                    value="{{ $cutting->size_38 }}">
                            </div>
                        </div>
                        <div class="col-md-6">

                            <div class="mb-3">
                                <label for="size_80" class="form-label">Size 40:</label>
                                <input type="number" name="size_80" id="size_80" class="form-control"
                                    value="{{ $cutting->size_80 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_42" class="form-label">Size 42:</label>
                                <input type="number" name="size_42" id="size_42" class="form-control"
                                    value="{{ $cutting->size_42 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_44" class="form-label">Size 44:</label>
                                <input type="number" name="size_44" id="size_44" class="form-control"
                                    value="{{ $cutting->size_44 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_46" class="form-label">Size 46:</label>
                                <input type="number" name="size_46" id="size_46" class="form-control"
                                    value="{{ $cutting->size_46 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_48" class="form-label">Size 48:</label>
                                <input type="number" name="size_48" id="size_48" class="form-control"
                                    value="{{ $cutting->size_48 }}">
                            </div>
                            <div class="mb-3">
                                <label for="size_50" class="form-label">Size 50:</label>
                                <input type="number" name="size_50" id="size_50" class="form-control"
                                    value="{{ $cutting->size_50 }}">
                            </div>

                            <div class="mb-3">
                                <label for="short_cutting" class="form-label">Short Cutting:</label>
                                <input type="text" name="short_cutting" id="short_cutting" class="form-control"
                                    value="{{ $cutting->short_cutting }}">
                            </div>
                            <div class="mb-3">
                                <label for="total" class="form-label">Total:</label>
                                <input type="number" name="total" id="total" class="form-control" step="0.01"
                                    value="{{ $cutting->total }}">
                            </div>
                            <div class="mb-3">
                                <label for="order_id" class="form-label">Order ID:</label>
                                <input type="number" name="order_id" id="order_id" class="form-control"
                                    value="{{ $cutting->order_id }}">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Update</button>
                </form>
            </div>
        </div>
    </div>
@endsection
