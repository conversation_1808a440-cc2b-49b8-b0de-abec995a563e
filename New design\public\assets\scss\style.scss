@import url("https://fonts.googleapis.com/css2?family=Lato:wght@100;300;400;700;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Roboto:wght@100;300;400;500;700;900&display=swap");
html {
    overflow-x: hidden;
    padding: 0px;
    margin: 0px;
}
$mss: "Manrope", sans-serif;
$rss: "Roboto", sans-serif;
$acc: #292be9;
.bg-green-one {
    background: #04f204 !important;
}
body {
    margin: 0;
    padding: 0 !important;
    font-family: "Roboto", sans-serif;
    font-size: 14px;
    line-height: 25px;
    letter-spacing: 0px;
    word-spacing: 0px;
    word-wrap: break-word;
    background: #fff;
}
#main-wrapper {
    background: #fff;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    padding: 0;
    font-weight: 400;
    color: #000000;
}

h1 {
    font-size: 36px;
    line-height: 36px;
}

h2 {
    font-size: 28px;
    line-height: 38px;
}

h3 {
    font-size: 22px;
    line-height: 32px;
}

h4 {
    font-size: 20px;
    line-height: 30px;
}

h5 {
    font-size: 18px;
    line-height: 28px;
}

h6 {
    font-size: 16px;
    line-height: 27px;
}

p {
    font-size: 14px;
    font-weight: 400;
    margin: 0;
    padding: 0;
    line-height: 24px;
    color: #505050;
}

ul,
ol {
    margin: 0;
    padding: 0;
}

ul li,
ol li {
    list-style: none;
}

a,
button,
.btn,
ol li a,
ul li a {
    letter-spacing: 0;
    text-decoration: none;
    outline: none !important;
    cursor: pointer;
    color: #000000;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

a:hover,
.btn:hover,
ol li a:hover,
ul li a:hover {
    outline: none !important;
    text-decoration: none;
    color: #000000;
}

.btn,
.btn:focus {
    outline: none !important;
    -webkit-box-shadow: 0 0 0 0em rgba(0, 123, 255, 0.25) !important;
    box-shadow: 0 0 0 0em rgba(0, 123, 255, 0.25) !important;
}

svg {
    fill: #000000;
    --svg-font-size: 14px;
    width: var(--svg-font-size);
    height: var(--svg-font-size);
    margin-top: -4px;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

/*=====================
    3 Side Bar
=======================*/

/* sidebar menu end css */
.side-bar {
    padding: 0 25px;
    position: fixed;
    top: 0;
    left: 0;
    width: 300px;
    height: 100%;
    z-index: 10;
    overflow-y: auto;
    overflow-x: hidden;
    opacity: 1;
    visibility: visible;
    background: #000;
    -webkit-box-shadow: 0px 0px 5px rgba(45, 51, 103, 0.15);
    -moz-box-shadow: 0px 0px 5px rgba(45, 51, 103, 0.15);
    box-shadow: 0px 0px 5px rgba(45, 51, 103, 0.15);
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    transition: all 0.4s ease;
    border-radius: 4px;
}
.side-bar-logo {
    padding: 20px;
    border-bottom: 1px solid rgba(69, 74, 84, 0.4);
    img {
        max-width: 100%;
    }
}
.close-btn {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    font-size: 20px;
    position: absolute;
    right: 15px;
    top: 15px;
    display: none;
}
.side-bar-manu {
    .dropdown {
        &::before {
            content: "\f105";
            font-family: "Font Awesome 5 Pro";
            font-weight: 400;
            color: #aaabae;
            position: absolute;
            right: 15px;
            font-size: 16px;
            display: block;
            z-index: 3;
            top: 10px;
        }
        &.active {
            > .dropdown-menu {
                display: block;
                .dropdown {
                    background: none;
                    &.active {
                        > a,
                        .active {
                            background: #292be9;
                        }
                    }
                }
            }
            .active {
                background: #292be9;
            }
        }
    }
    li {
        position: relative;
        margin: 0;
        font-size: 12px;
        margin: 5px 0;
        a {
            position: relative;
            display: block;
            padding: 10px 20px;
            color: #fff;
            border-radius: 4px;
            z-index: 2;
            display: flex;
            align-items: center;
            font-size: 16px;
            .sidebar-icon {
                display: inline-block;
                margin-right: 10px;
            }
            i {
                margin-right: 10px;
            }
        }
        &:hover,
        &.active {
            &:before {
                transform: rotate(90deg);
                transition: all 0.4s ease;
                color: #fff;
            }
            > a {
                background-color: #292be9;
                color: #fff !important;
            }
        }
        .dropdown-menu {
            margin-left: 20px;
            border-left: 1px solid #fff;
            li .dropdown-menu {
                margin-left: 0 !important;
                border: none !important;
                .dropdown {
                    .dropdown-menu {
                        .dropdown {
                            background: none !important;
                            padding-left: 0 !important;
                        }
                    }
                }
            }
            li {
                padding-left: 15px;
                a {
                    color: #fff;
                    padding: 5px 15px;
                    position: relative;
                    // &::before {
                    //   content: "\f105";
                    //   font-family: "Font Awesome 5 Pro";
                    //   font-weight: 400;
                    //   color: #aaabae;
                    //   position: absolute;
                    //   right: 15px;
                    //   font-size: 16px;
                    //   display: block;
                    //   z-index: 3;
                    //   top: 6px;
                    // }
                    &:hover,
                    &.active {
                        color: #fff !important;
                    }
                }
                li {
                    &:hover,
                    &.active {
                        &:before {
                            transform: rotate(90deg);
                        }
                        > a {
                            background-color: #292be9;
                            color: #fff !important;
                        }
                    }
                }
            }
        }
    }
    .dropdown-menu {
        padding: 0;
        border-radius: 0;
        background: transparent;
    }
}
.side-bar-manu .dropdown.active > .dropdown-menu .dropdown {
    background: none !important;
}
.side-bar {
    .dropdown-menu {
        position: unset;
    }
    li ul {
        display: none;
    }
}
.sidebar-icon {
    img {
        width: 14px;
    }
}
.side-bar.active {
    width: 80px;
    .side-bar-manu .dropdown::before {
        opacity: 0;
    }
    .side-bar-manu li a .sidebar-icon {
        margin-right: 30px;
        position: relative;
        left: -10px;
    }
    .sidebar-icon img {
        width: 22px;
    }
    &:hover {
        width: 300px;
        .side-bar-manu .dropdown::before {
            opacity: 1;
        }
        .side-bar-manu li a .sidebar-icon {
            margin-right: 10px;
            position: relative;
            left: 0px;
        }
        .sidebar-icon img {
            width: 16px;
        }
    }
}
.section-container {
    transition: all 0.3s ease;
}
.section-container.active {
    padding-left: 80px;
}
/* sidebar menu end css */
/* header  start css */
.header-wrapper {
    display: grid;
    grid-template-columns: 33% 33% 33%;
}
.main-header-section {
    padding: 14px 20px;
    background: #f4f4f4;
    box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
    .profile-info {
        img {
            height: 50px;
            width: 50px;
            min-width: 50px;
            border-radius: 50%;
            span {
                font-size: 16px;
            }
        }

        .dropdown-menu {
            background: #fff;
            box-shadow: 0px 0px 7px -5px rgba(0, 0, 0, 0.75);
            min-width: 160px;
            li {
                border-bottom: 1px solid #454a54;
                font-size: 12px;
                padding: 0;
                display: block;
                border-bottom: 1px solid #ccc;
                a {
                    display: block;
                    color: #000;
                    padding: 8px 10px;
                    i {
                        margin-right: 10px;
                    }
                    &:hover {
                        color: #292be9;
                    }
                }
            }
        }
    }
    .dropdown-menu {
        top: 60% !important;
        border: none;
        border-radius: 2px !important;
        padding: 0;
        min-width: 220px;
    }
}
.header-left {
    display: flex;
    align-items: center;
}
.header-right {
    display: flex;
    justify-content: flex-end;
}
.language-change {
    display: flex;
    align-items: center;
    img {
        height: 30px;
        width: 30px;
        border-radius: 50%;
    }
    .nice-select {
        background: none;
        border: none;
    }
    .nice-select:after {
        border-color: #000;
        height: 7px;
        top: 46%;
        width: 7px;
    }
}
.header-calender {
    height: 45px;
    width: 45px;
    line-height: 45px;
    text-align: center;
    cursor: pointer;
}
.notifications.dropdown > a {
    position: relative;
    display: block;
    padding: 0;
    color: #737881;
    height: 45px;
    width: 45px;
    text-align: center;
    line-height: 45px;
}
.notifications.dropdown > a.show {
    background-color: #eee;
    -webkit-border-radius: 3px 3px 0 0;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px 3px 0 0;
    -moz-background-clip: padding;
    border-radius: 3px 3px 0 0;
    background-clip: padding-box;
}
.bg-info {
    background: #21a9e1;
    color: #fff;
}
.text-red {
    color: red !important;
}
.notifications {
    margin-left: 10px;

    .dropdown-toggleer {
        position: relative;
        span {
            display: block;
            height: 17px;
            width: 17px;
            border-radius: 50%;
            line-height: 17px;
            text-align: center;
            font-size: 10px;
            position: absolute;
            top: 2px;
            right: 6px;
        }
    }
    .dropdown-menu {
        top: 0 !important;
        background: #eee;
        min-width: 370px;
    }
    ul {
        li {
            a {
                padding: 10px 20px;
                display: block;
                border-top: 1px solid #ddd;
                span,
                strong {
                    display: block;
                    font-size: 12px;
                }
                span {
                    color: #888888;
                    line-height: 1;
                    margin-top: 5px;
                }
            }
        }
    }
}
.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #eee;
    p {
        font-size: 12px;
    }
    a {
        font-size: 12px;
    }
}
.bg-red {
    background: #ee4749;
    color: #fff;
}
.notification-footer {
    background: #fff;
    padding: 10px;
    line-height: 1;
    a {
        font-size: 12px;
        line-height: 1;
    }
}
.msg-items {
    display: flex !important;
    align-items: center;
    .content {
        width: 100%;
        strong {
            display: flex;
            span {
                margin: 0;
                margin-left: 10px;
                font-weight: 400;
            }
        }
    }
    img {
        height: 50px;
        width: 50px;
        min-width: 50px;
        border-radius: 50%;
        margin-left: 15px;
    }
}
.bg-yellow {
    background: yellow;
}
.sidebar-opner {
    cursor: pointer;
    font-size: 20px;
    color: #000;
    margin-right: 20px;
}
.header-middle {
    input {
        background: none !important;
        box-shadow: none !important;
    }
}
.search-btn {
    background: #292be9 !important;
    color: #fff !important;
}
/* header  end css */

.section-container {
    padding-left: 300px;
    padding-bottom: 80px;
}
.erp-state-overview-section {
    padding-top: 30px;
}
.erp-state-overview-wrapper {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
}
.container-fluid {
    padding: 0 30px;
}
.state-overview-box {
    display: flex;
    align-items: center;
    background: #ffffff;
    box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.24),
        0px 3px 8px -1px rgba(71, 50, 50, 0.05);
    border-radius: 25px;
    padding: 10px;
    gap: 15px;
    .icons {
        height: 100px;
        width: 100px;
        min-width: 100px;
        background: rgba(255, 16, 188, 0.1);
        border-radius: 17px;
        text-align: center;
        line-height: 100px;
    }
    h2 {
        font-weight: 500;
        font-size: 30px;
        line-height: 35px;
        color: #0c1928;
    }
    p {
        font-weight: 400;
        font-size: 18px;
        line-height: 21px;
        color: #555e69;
    }
    &:nth-child(2) {
        .icons {
            background: rgba(255, 206, 42, 0.1);
        }
    }
    &:nth-child(3) {
        .icons {
            background: rgba(23, 218, 148, 0.1);
        }
    }
    &:nth-child(4) {
        .icons {
            background: rgba(23, 218, 148, 0.1);
        }
    }
    &:nth-child(5) {
        .icons {
            background: rgba(3, 169, 244, 0.1);
        }
    }
}
.erp-graph-box {
    margin-top: 30px;
    background: #ffffff;
    box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.24),
        0px 3px 8px -1px rgba(71, 50, 50, 0.05);
    border-radius: 25px;
    .graph-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(255, 73, 128, 0.1);
        box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
        border-radius: 25px 25px 0px 0px;
        padding: 15px 20px;
        h4 {
            font-weight: 400;
            font-size: 16px;
            line-height: 21px;
            color: #0c1928;
        }
        .nice-select {
            height: auto;
            line-height: 1;
            background: none;
            border: none;
            font-size: 16px;
            color: #0c1928;
        }
        .nice-select:after {
            height: 7px;
            width: 7px;
            border-color: #000;
            top: 45%;
        }
    }
    .erp-box-content {
        padding: 15px;
        overflow: auto;
    }
}
.yearly-status {
    .graph-header {
        background: rgba(175, 73, 255, 0.1);
    }
}

.top-customer {
    .graph-header {
        background: rgba(209, 255, 26, 0.2);
        box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
    }
}
.new-order {
    .graph-header {
        background: rgba(14, 180, 255, 0.1);
        color: #0eb4ff;
    }
}
.table-profile {
    display: flex;
    align-items: center;
    .user-img {
        margin-right: 10px;
        img {
            height: 40px;
            width: 40px;
            min-width: 40px;
            border-radius: 50%;
        }
    }
    span {
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #03a9f4;
        display: block;
        opacity: 0.6;
    }
    strong {
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
        color: #0c1928;
        opacity: 0.7;
    }
}
.table-profile-order {
    display: flex;
    align-items: center;
    text-align: left;
    .user-img {
        margin-right: 10px;
        img {
            height: 40px;
            width: 40px;
            min-width: 40px;
            border-radius: 5px;
        }
    }
    span {
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #03a9f4;
        display: block;
        opacity: 0.6;
    }
    strong {
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
        color: #0c1928;
        opacity: 0.7;
    }
}
.top-customer-table {
    td {
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #0c1928;
        text-align: center;
        vertical-align: middle;
        padding: 5px;
        border: none;
    }
    a {
        color: #03a9f4;
    }
}
.status-btn {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    padding: 8px 20px;
    border-radius: 8px;
    border: none;
    line-height: 1;
    border-radius: 8px;
    min-width: 90px;
}
.pending {
    background: rgba(255, 164, 28, 0.2) !important;
    color: #ffa41c !important;
}
.aproved {
    background: rgba(37, 212, 169, 0.1) !important;
    color: #25d4a9 !important;
}

/* table css start css */
.erp-table-section {
    padding-top: 30px;
    font-family: $mss;
}
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(126, 126, 126, 0.3);
    h4 {
        font-weight: 600;
        font-size: 20px;
        line-height: 30px;
        text-transform: capitalize;
        color: #191919;
        font-family: $mss;
    }
    a {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #7e7e7e;
        padding: 5px 15px;
    }
    .button-group {
        border-bottom: 1px solid #292be9;
        a {
            background: none !important;
            color: #7e7e7e !important;
            border-radius: 6px 6px 0px 0px;
            &.active {
                background: $acc !important;
                color: #fff !important;
            }
        }
    }
}
.add-order-btn {
    background: $acc !important;
    color: #fff !important;
    border-radius: 6px 6px 0px 0px;
}
.grid-5 {
    align-items: center;
    display: flex;
}
.table-top-form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    form {
        max-width: 1100px;
    }
    .form-control {
        height: 32px;
        line-height: 1;
        box-shadow: none !important;
    }
    .nice-select .list {
        width: 100%;
    }
}
.input-wrapper {
    position: relative;
    display: flex;
    // flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
    > .form-control,
    .form-select {
        position: relative;
        flex: 1 1 auto;
        width: 1%;
        min-width: 0;
        margin: 0 10px;
    }
}
.table-top-btn-group {
    ul {
        display: flex;
        align-items: center;
        gap: 10px;
        a {
            height: 24px;
            width: 24px;
            background: rgba(0, 165, 81, 0.1);
            border-radius: 4px;
            text-align: center;
            display: block;
            padding: 0;
        }
        li {
            &:nth-child(2) {
                a {
                    background: rgba(154, 93, 252, 0.15);
                }
            }
            &:nth-child(3) {
                a {
                    background: rgba(241, 60, 60, 0.1);
                }
            }
            &:nth-child(4) {
                a {
                    background: rgba(255, 132, 0, 0.1);
                }
            }
        }
    }
}
.responsive-table {
    overflow: auto;
    margin-top: 20px;
}
.table {
    border: 1px solid rgba(31, 31, 31, 0.1);
    th {
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        text-transform: capitalize;
        color: #191919;
        background: rgba(31, 31, 31, 0.05);
    }
    th,
    td {
        border-bottom: 1px solid rgba(31, 31, 31, 0.1);
        white-space: nowrap;
        vertical-align: middle;
        text-align: center;
    }
    td {
        font-weight: 400;
        font-size: 12px;
        line-height: 22px;
        color: #7e7e7e;
        text-align: center;
    }
}
.table-two {
    td {
        font-weight: 500;
        font-size: 10px;
        line-height: 22px;
        text-transform: capitalize;
        color: #191919;
        vertical-align: middle !important;
    }
    th,
    td {
        border: 1px solid rgba(0, 0, 0, 0.5);
        background: none;
    }
    th {
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        text-transform: capitalize;
        color: #191919;
    }
    thead {
        td {
            font-weight: 500;
            font-size: 12px;
            line-height: 20px;
            text-transform: capitalize;
            color: #191919;
            white-space: pre-wrap;
            vertical-align: bottom;
        }
    }
}
.table-img {
    height: 40px;
    max-width: 100%;
}
.table-action {
    button {
        font-size: 22px;
        background: none;
        border: none;
        outline: none;
        text-align: center;
        display: block;
        width: 100%;
    }
    .dropdown-menu {
        min-width: 180px;
        padding: 0;
        background: #ffffff;
        box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        padding: 10px;
        padding: 0;
        a {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            color: #7e7e7e;
            display: block;
            padding: 10px 15px;
            i {
                margin-right: 7px;
            }
            &:hover {
                background: #eee;
            }
        }
    }
}
.pagination {
    justify-content: flex-end;
    .page-link {
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        color: #7e7e7e;
        border: none;
        box-shadow: none;
        background: none;
        &.active,
        &:hover {
            color: $acc;
            background: none;
        }
    }
}
.ui-widget.ui-widget-content {
    z-index: 3 !important;
}

.order-form-section {
    label {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        text-align: center;
        color: #191919;
        background: #fff;
        position: relative;
        left: 20px;
        top: 11px;
        padding: 0 10px;
        z-index: 2;
        line-height: 1;
    }
    .form-control {
        box-shadow: none;
        background: #ffffff;
        border: 1px solid #e4e5e7;
        border-radius: 6px;
        padding: 12px 15px;
        font-weight: 400;
        &::placeholder {
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            color: #7e7e7e;
        }
    }
    .table-select {
        height: 50px;
        line-height: 24px;
    }
    h6 {
        font-weight: 600;
    }
}
.upload-img {
    margin-top: 10px;
    top: 0 !important;
    left: 0 !important;
    background: #ffffff;
    border: 2px solid rgba(126, 126, 126, 0.4);
    border-radius: 10px;
    display: block;
    height: 206px;
    cursor: pointer;
    position: relative;
    text-align: center;
    i {
        position: absolute;
        font-size: 30px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    p {
        position: absolute;
        bottom: 15px;
        left: 0;
        width: 100%;
        font-weight: 400;
        line-height: 22px;
        text-transform: capitalize;
        color: #555e69;
    }
    .image-thumb {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
.add-color-btn {
    border: 1px solid #292be9;
    border-radius: 3px;
    padding: 2px 8px;
    font-size: 10px;
    color: #292be9;
    cursor: pointer;
    display: inline-block;
    line-height: 1.6;
}
input[type="date"] {
    min-width: 125px !important;
}
.theme-btn {
    min-width: 210px;
    background: #292be9;
    border-radius: 6px;
    color: #fff;
    border: none;
    padding: 10px;
    display: inline-block;
    text-align: center;
}

.border-btn {
    border: 1px solid #7e7e7e;
    border-radius: 6px;
    background: none;
    color: #7e7e7e;
}

.table-title {
    text-align: center;
    background: #ffe6cc;
    border-radius: 5px 5px 0px 0px;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    text-transform: capitalize;
    color: #191919;
    padding: 5px 10px;
}
.status-btn {
    display: inline-block;
    font-weight: 500;
    font-size: 13px;
    line-height: 18px;
    text-transform: capitalize;
    padding: 3px 10px;
    border-radius: 4px;
    min-width: 72px;
}
.pending2 {
    color: #555e69;
    background: rgba(85, 94, 105, 0.1);
}
.complete {
    background: rgba(6, 178, 75, 0.1);
    color: #06b34b;
}
.canceled {
    background: rgba(255, 98, 125, 0.1);
    color: #ff627d;
}
.select-tow {
    height: 36px;
    line-height: 16px;
    width: 100%;
    font-weight: 600;
    .list {
        width: 100%;
    }
}
.mt-30 {
    margin-top: 30px;
}
.mb-30 {
    margin-bottom: 30px;
}
.clr-black {
    color: #191919 !important;
    font-weight: 500;
    td,
    th {
        color: #191919 !important;
    }
}
b,
strong {
    color: #191919 !important;
}
.small-table {
    tr {
        td {
            font-family: "Manrope";
            font-style: normal;
            font-weight: 500;
            font-size: 12px;
            line-height: 20px;
            text-transform: capitalize;
            color: #191919;
            text-align: left;
            padding: 5px 10px;
            word-wrap: wrap;
            white-space: pre-wrap !important;
            // color: #7E7E7E;
            &:first-child {
                max-width: 270px;
                width: 200px;
            }
        }
    }
    tbody {
        th {
            text-align: left;
            font-size: 12px;
        }
    }
    th {
        background: none;
        word-wrap: wrap;
        white-space: pre-wrap !important;
        color: #191919;
        font-weight: 700;
        font-size: 12px;
    }
}
.clr-gray {
    td {
        color: #7e7e7e !important;
    }
}
.theme-border {
    border-color: $acc;
    color: $acc;
}
.title-four {
    text-align: center;
    font-family: "Manrope";
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    text-transform: capitalize;
    color: #191919;
    background: rgba(31, 31, 31, 0.05);
}

.signature {
    max-width: 200px;
    margin: 0 auto;
    border-top: 1px solid #000;
    text-align: center;
}
.add-suplier-wrapper {
    position: relative;
    .nice-select:after {
        right: 60px;
    }
    .add-suplier-modal {
        position: absolute;
        right: 15px;
        top: 14px;
        height: 24px;
        width: 24px;
        line-height: 24px;
        text-align: center;
        background: rgba(255, 132, 0, 0.5);
    }
}
.nice-select:after {
    height: 10px;
    width: 10px;
    top: 43%;
    right: 20px;
}
.modal-body {
    padding-top: 0;
}
.modal-content {
    padding-bottom: 20px;
}
.add-suplier-modal-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 30px;
}
.add-profile-photo {
    min-width: 160px;
    width: 160px;
    text-align: center;
}
.add-profile-photo-wrapper {
    position: relative;
    background: none;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    margin-top: 35px;
    max-width: 160px;
    height: 160px;
    cursor: pointer;
    .image-wrapper {
        height: 150px;
        width: 150px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid $acc;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    .icons {
        position: absolute;
        height: 50px;
        width: 50px;
        border-radius: 50%;
        border: 2px solid $acc;
        text-align: center;
        line-height: 45px;
        font-size: 20px;
        color: #919699;
        position: absolute;
        bottom: 0;
        right: 0;
        background: #fff;
    }
}
.costing-price-wrapper {
    padding: 30px 0;
}
.costing-list {
    li {
        display: block;
        display: flex;
        align-items: center;
        margin-top: 15px;
    }
    span {
        min-width: 165px !important;
        display: inline-block;
        font-family: "Manrope";
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 18px;
        text-transform: capitalize;
        color: #191919;
        &:nth-child(2) {
            text-align: center;
            min-width: 40px !important;
        }
    }
}
.mark-style {
    background: #ffe6cc !important;
    color: #7e7e7e !important;
}
.t-shirt-thumb {
    max-width: 240px;
    height: 250px;
    border: 1px solid rgba(126, 126, 126, 0.5);
    border-radius: 6px;
    text-align: center;
    padding: 15px 25px;
    margin-left: auto;
    img {
        width: 100%;
        height: 100%;
    }
}

.table-title-three {
    background: #f4f4f4;
    border-radius: 5px 5px 0px 0px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    padding: 5px 15px;
    margin-top: 25px;
    h5 {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #191919;
    }
}
.table-two.table-bordered {
    border-color: #fff;
    td,
    th {
        border: 1px solid rgba(126, 126, 126, 0.4);
    }
}
.t-header {
    background: #f4f4f4 !important;
    color: #191919 !important;
    font-weight: 500 !important;
}
.bank-account-info {
    padding: 15px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    margin-top: 20px;
    .costing-list span {
        min-width: 130px;
    }
    .add-order-btn {
        font-size: 14px;
        width: auto;
        margin-left: auto;
    }
}
.title-five {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    padding-bottom: 0;
}

.add-suplier-modal-wrapper {
    .nice-select {
        line-height: 23px;
        .list {
            width: 100%;
        }
    }
}

.order-form-section {
    .costing-list {
        margin: 20px auto;
        span {
            min-width: 180px;
        }
    }
}
.text-success {
    color: #06b34b !important;
}
.view-cheque-items {
    padding: 15px 30px;
    background: #f4f4f4;
    border-radius: 10px;
    margin-bottom: 30px;
    .title-seven {
        font-family: "Manrope";
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 22px;
        text-transform: capitalize;
        color: #191919;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(126, 126, 126, 0.3);
        margin-bottom: 15px;
    }
}
.bank-status-list {
    li {
        display: flex;
        align-items: center;
        margin: 7px 0;
        span {
            font-family: "Manrope";
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 22px;
            display: inline-block;
            margin-left: 15px;
            text-transform: capitalize;
            color: #0c1928;
        }
        .w-140 {
            color: #7e7e7e;
            margin: 0;
        }
    }
}
.w-140 {
    width: 160px;
    display: inline-block;
    margin: 0;
}
.loan-view-modal-wrapper {
    .bank-status-list {
        span {
            color: #7e7e7e !important;
            font-size: 14px !important;
        }
        .w-140 {
            color: #0c1928 !important;
        }
    }
}
th {
    table {
        th {
            background: none !important;
            padding: 7px 0 !important;
            text-align: center;
            border: 1px solid rgba(31, 31, 31, 0.1);
        }
    }
}
.top-customer-table {
    .table th,
    .table td {
        border: none !important;
    }
    .table {
        border: none !important;
    }
}

/*--------------------------------------
    - login css start
----------------------------------------*/
.mybazar-login-section {
    height: 100vh;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;

    .mybazar-login-wrapper {
        max-width: 650px;
        margin: 0 auto;
        width: 100%;
        position: relative;
        z-index: 2;
    }
    .mybazar-login-wrapper:after {
        position: absolute;
        content: "";
        display: block;
        left: 0;
        top: -3px;
        height: 100%;
        width: 100%;
        background: #292be9;
        border-radius: 32px;
        z-index: -1;
    }
    .mybazar-login-wrapper .login-header {
        padding: 30px;
        border-bottom: 1px solid rgba(12, 25, 40, 0.2);
        text-align: center;
    }
    .mybazar-login-wrapper .login-header img {
        max-width: 100%;
    }

    .login-wrapper {
        position: relative;
        z-index: 5;
        background: #fff;
        -webkit-box-shadow: 0px 20px 50px rgba(12, 25, 40, 0.1);
        box-shadow: 0px 20px 50px rgba(12, 25, 40, 0.1);
        border-radius: 30px;
    }

    .login-body {
        padding: 30px;
    }
    .login-body h2 {
        text-align: center;
        font-weight: 500;
        font-size: 20px;
        color: rgba(12, 25, 40, 0.7);
        margin-bottom: 30px;
        line-height: 1;
    }
    .login-body p {
        color: #0c1928;
        opacity: 0.5;
        text-align: center;
    }
    .login-body .input-group {
        position: relative;
        margin: 0;
        margin-top: 10px;
    }
    .login-body .input-group span {
        position: absolute;
        top: 50%;
        left: 15px;
        z-index: 5;
        -webkit-transform: translate(0, -50%);
        transform: translate(0, -50%);
    }
    .login-body .input-group .hide-pass {
        position: absolute;
        top: 50%;
        left: auto;
        right: 15px;
        z-index: 5;
        -webkit-transform: translate(0, -50%);
        transform: translate(0, -50%);
    }
    .login-body .input-group .form-control {
        padding: 12px 15px;
        -webkit-box-shadow: none;
        box-shadow: none;
        padding-left: 45px;
        height: 50px;
        border-radius: 12px !important;
    }

    .login-btn {
        display: block;
        font-size: 16px;
        color: #fff !important;
        background: #292be9;
        border-radius: 12px;
        padding: 13px;
        width: 100%;
        margin-top: 20px;
        border: 1px solid transparent;
        height: 50px;
    }
    .login-btn:hover,
    .login-btn:focus {
        background: #fff !important;
        color: #000 !important;
        border: 1px solid #292be9;
    }

    .button-group {
        display: -ms-grid;
        display: grid;
        -ms-grid-columns: auto 10px auto 10px auto 10px auto;
        grid-template-columns: auto auto auto auto;
        gap: 10px;
    }
    .button-group a {
        background: #25d4a9;
    }
    .button-group a:nth-child(2) {
        background: #03a9f4;
    }
    .button-group a:nth-child(3) {
        background: #292be9;
    }
    .button-group a:nth-child(4) {
        background: #ff55b1;
    }

    .login-footer {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        margin-top: 20px;
    }
    .login-footer a {
        font-size: 16px;
        font-weight: 400;
        color: #0c1928;
        display: inline-block;
        margin: 5px;
    }
    .login-footer a span {
        margin-right: 5px;
    }
    .login-footer a:hover {
        text-decoration: underline;
    }

    @media (max-width: 575px) {
        .button-group {
            -ms-grid-columns: 50% auto;
            grid-template-columns: 50% auto;
        }
        .maan-main-content {
            padding: 25px 10px;
        }
    }
    .hide-pass img:last-child {
        display: none;
    }
    .show-pass img:first-child {
        display: none;
    }
    .show-pass img:last-child {
        display: block;
    }
    .hide-pass {
        display: flex;
    }
}

/*--------------------------------------
    - login css end
----------------------------------------*/
.login-button-list {
    margin-top: 20px;
    .theme-btn {
        min-width: 140px;
        border-radius: 12px;
        color: #fff !important;
    }
    li {
        display: inline-block;
        &:nth-child(1) {
            a {
                background: #25d4a9;
            }
        }
        &:nth-child(2) {
            a {
                background: #03a9f4;
            }
        }
        &:nth-child(3) {
            a {
                background: #292be9;
            }
        }
        &:nth-child(4) {
            a {
                background: #ff55b1;
            }
        }
        &:nth-child(5) {
            a {
                background: #0f466d;
            }
        }
        &:nth-child(6) {
            a {
                background: rgba(133, 24, 20, 0.8);
            }
        }
        &:nth-child(7) {
            a {
                background: #1d89e3;
            }
        }
    }
    ul {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
}
.login-footer {
    img {
        margin-right: 10px;
    }
}
.fa-print {
    // color:  #292BE9;
}

.delete-massage {
    text-align: center;
}
.aler-icons {
    height: 50px;
    width: 50px;
    line-height: 50px;
    color: red;
    border: 1px solid red;
    border-radius: 50%;
    margin: 0 auto;
    margin-bottom: 15px;
}
.erp-dashboard-profile {
    img {
        width: 100%;
    }
    .profile-bg {
        height: 150px;
        img {
            height: 150px;
            object-fit: cover;
        }
    }
    .profile-img {
        height: 80px;
        width: 80px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto;
        margin-top: -40px;
        border: 2px solid #292be9;
    }
}

.erp-dashboard-profile-section {
    padding: 20px;
}

.order-summary-tab {
    margin-top: -34px;
}

/* print css start css */
.print-wrapper {
    .table th,
    .table td {
        white-space: pre-wrap;
        vertical-align: middle;
    }
    .daily-production-table-print {
        th,
        td {
            padding: 0 !important;
        }
    }
    .container {
        // max-width: 780px;
        margin: 0 auto;
    }
    .ml-auto {
        margin-left: auto;
    }
}
.print-signature-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 100px;
    p {
        border-top: 1px solid #000;
        width: 200px;
        text-align: center;
    }
}
.bg-gray {
    background: rgba(31, 31, 31, 0.05) !important;
    color: #000 !important;
}
/* print css end css*/

.table-footer-last {
    max-width: 350px;
    margin-left: auto;
    margin-top: 100px;
    td {
        text-align: left;
        font-weight: 500;
        color: #000;
        &:last-child {
            text-align: right;
            font-weight: 700;
            color: #000;
        }
    }
}

.modal-btn-ctg {
    height: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    color: #fff !important;
    background: $acc;
}
.input-group {
    .form-control {
        border-top-left-radius: 6px !important;
        border-bottom-left-radius: 6px !important;
    }
}
.daily-transaction-between-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
    .between-wrapper {
        display: flex;
        align-items: center;
        border: 1px solid #ced4da;
        overflow: hidden;
        border-radius: 6px;
        input {
            border: none;
            padding: 0;
            margin: 0 10px;
            min-width: 90px !important;
            text-align: center;
            outline: none;
        }
        input[type="date"]::-webkit-inner-spin-button,
        input[type="date"]::-webkit-calendar-picker-indicator {
            display: none;
            -webkit-appearance: none;
        }

        span {
            background: #6c6d78;
            display: inline-block;
            padding: 6px 10px;
            color: #fff;
        }
        .nice-select {
            height: 37px;
            border-radius: 6px;
        }
    }
}
.this-month-select {
    padding-left: 0;
    .current {
        font-weight: 500;
    }
}

.total-count-area {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
}
.total-count-area .count-item {
    border-radius: 10px;
    max-width: 190px;
    padding: 15px 20px;
    width: 100%;
}
.total-count-area .count-item h5 {
    font-weight: 600;
    font-size: 23px;
    line-height: 31px;
    color: #2e2e3e;
    margin-bottom: 5px;
}
.total-count-area .count-item p {
    font-size: 16px;
    font-weight: 500;
}
.light-blue {
    background: rgba(45, 176, 246, 0.2);
}
.light-green {
    background: rgba(21, 205, 117, 0.2);
}
.light-orange {
    background: rgba(255, 140, 0, 0.2);
}
.light-red {
    background: rgba(255, 37, 37, 0.2);
}
.input-group
    > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
        .valid-feedback
    ):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: 0;
}
.add-suplier-modal-wrapper {
    .input-group {
        border: 1px solid #ced4da;
        border-radius: 6px;
        margin-left: 0 !important;
        .nice-select {
            border: none;
            background: none;
        }
        .input-group-text {
            border: none;
        }
    }
}
.service-btn-possition {
    background: $acc !important;
    color: #fff !important;
    font-size: 20px;
    height: 40px;
    width: 40px;
    border: none;
    outline: none;
}
.grid-5 {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
}
.grid-10 {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 15px;
}
.grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}
.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}
.feature-row {
    position: relative;
    padding-right: 60px;
    .remove-btn-features {
        position: absolute;
        top: 10px;
        right: 0;
    }
}
.form-check-input:focus {
    box-shadow: none;
}
.form-switch {
    margin: 0;
    padding: 0;
    .form-check-input {
        margin: 0 auto;
        padding: 0;
        height: 20px;
        width: 40px !important;
        float: none;
        border: 1px solid #ddd !important;
    }
}
.nice-select,
select {
    min-width: 120px;
}
.print-btn {
    width: auto;
    min-width: 30px;
    padding: 8px 15px;
    i {
        margin-right: 6px;
    }
}
.ledger-detais-date {
    max-width: 300px;
    margin: 0 auto;
    margin-top: 20px;
    .input-wrapper-between {
        input {
            min-width: unset !important;
            width: 90px;
        }
        small {
            font-weight: 500;
            font-size: 15px;
        }
    }
}
.form-switch .form-check-input:checked {
    background-color: #292be9;
}

.commercial-invoice {
    tr,
    td,
    th {
        font-size: 16px;
        text-align: left;
        white-space: normal !important;
        height: 40px;
        p {
            font-size: 16px;
            line-height: 30px;
        }
    }
    &.text-center {
        td,
        th,
        tr {
            text-align: center;
        }
    }
}

.sample-form-wrp {
    padding-right: 180px;
    .service-btn-possition {
        position: absolute;
        right: 60px;
        top: 40px;
    }
    .remove-btn-features {
        top: 40px;
        right: 15px;
    }
}
table {
    input {
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        width: 100% !important;
        font-size: 12px !important;
    }
}
.mw-1000 {
    min-width: 1000px;
}
.table-form-section {
    position: relative;
    .responsive-table {
        padding-left: 20px;
    }
    .add-btn-one,
    .add-btn-two,
    .add-btn-three,
    .add-btn-four,
    .add-btn-five,
    .add-btn-six,
    .add-btn-seven {
        position: absolute;
        left: -20px;
        top: 0px;
        height: 20px;
        width: 20px;
        background: rgba(255, 132, 0, 0.5);
        border-radius: 3px;
        color: #000;
        text-align: center;
        line-height: 20px;
        cursor: pointer;
    }
}
td.position-relative,
tr.position-relative {
    padding: 0;
    td {
        padding: 0;
    }
}
.w-120 {
    min-width: 120px;
}
.form-table-sm {
    tr {
        td {
            &:first-child {
                width: 140px;
            }
        }
    }
}
.tr-remove-btn {
    position: absolute;
    left: -20px;
    top: 30px;
    color: red;
    cursor: pointer;
    height: 20px;
    width: 20px;
    text-align: center;
    line-height: 20px;
    display: none;
}
.dataTables_wrapper {
    .dt-buttons {
        float: right;
        margin-bottom: 30px;
        button {
            border: none;
            -webkit-box-shadow: none;
            box-shadow: none;
            outline: none;
            background: none;
            font-size: 18px;
            color: rgba(12, 25, 40, 0.5);
        }
    }
    .dataTables_filter {
        float: left;
        margin-bottom: 30px;
        width: 200px;
        margin-left: 15px;
        label {
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #fff;
            height: 40px;
            input {
                position: absolute;
                left: 0;
                bottom: 0;
            }
        }
    }
    .dataTables_length {
        float: left;
        margin-bottom: 30px;
        width: 150px;
        label {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #fff;
            position: relative;
            min-height: 40px;
            select {
                position: absolute;
                left: 0;
                bottom: 0;
                z-index: 1;
            }
        }
    }
    select,
    input {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        outline: none;
        padding-right: 15px;
    }
}
.dataTables_info {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.7;
    float: left;
}
.dataTables_paginate {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    float: right;
}
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 3px 10px;
    display: inline-block;
    border: none !important;
    border: 1px solid #eee !important;
    line-height: 20px;
    margin: 0;
    color: #f88808 !important;
}
.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #f88808;
    color: #fff !important;
}
.nice-select {
    .list {
        width: 100%;
    }
}
.A4-paper {
    max-width: 1100px;
    margin: 0 auto;
}
.print-img {
    margin-left: 50px;
}
.w-400 {
    max-width: 400px;
}

/* invoice css start */
.invoice-section {
    max-width: 800px;
    margin: 0 auto;
}
.invoice-container {
    padding: 0 20px;
}
.invoice-header {
    background: #dbeef4;
    padding: 20px 0;
    h1 {
        font-family: "Manrope";
        font-weight: 600;
        font-size: 24px;
        line-height: 68px;
        color: #191919;
        text-align: right;
    }
    h2 {
        font-family: "Manrope";
        font-weight: 700;
        font-size: 20px;
        line-height: 30px;
        color: #191919;
    }
    p {
        font-family: "Manrope";
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #555e69;
    }
}
.invoice-address-section {
    padding: 20px 0;
    padding-top: 30px;
    li {
        // display: flex;
        font-family: "Manrope";
        font-weight: 400;
        font-size: 13px;
        line-height: 23px;
        color: #0c1928;
        p,
        span {
            font-family: "Manrope";
            font-weight: 400;
            font-size: 13px;
            line-height: 23px;
            color: #0c1928;
        }
    }
}
.invoice-table {
    thead {
        th {
            border: 1px solid #7e7e7e;
            background: #dbeef4;
            font-style: normal;
            font-weight: 500;
            font-size: 10px;
            line-height: 24px;
            color: #191919;
            padding: 5px 10px;
        }
    }
    tbody {
        td {
            font-style: normal;
            font-weight: 400;
            font-size: 8px;
            line-height: 20px;
            color: #475467;
            border: 1px solid #7e7e7e;
            text-align: left;
            white-space: normal;
            padding: 5px 10px;
            &:last-child {
                text-align: right;
            }
        }
    }
    tfoot {
        th {
            border: 1px solid #7e7e7e;
            background: #dbeef4;
            font-style: normal;
            font-weight: 600;
            font-size: 10px;
            line-height: 24px;
            color: #191919;
            padding: 5px 10px;
        }
        td {
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            color: #475467;
            border: 1px solid #7e7e7e;
            text-align: left;
            white-space: normal;
            &:last-child {
                text-align: right;
            }
            span,
            p {
                max-width: 100% !important;
            }
        }
    }
}
.invoice-bank-details {
    margin-top: 30px;
    h4 {
        display: inline-block;
        font-family: "Manrope";
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 33px;
        color: #0c1928;
        border-bottom: 1px solid #0c1928;
        margin-bottom: 10px;
    }
    li {
        margin: 0;
        span,
        p {
            max-width: 100%;
            color: #0c1928;
        }
    }
}
.invoice-others-section {
    padding: 40px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h6 {
        font-family: "Manrope";
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        max-width: 220px;
        line-height: 1.4;
        color: #0c1928;
    }
}
.invoice-signature-section {
    display: flex;
    justify-content: space-between;
    padding: 30px 0;
    p {
        display: inline-block;
        color: #0c1928;
        border-top: 1px solid #0c1928;
    }
}
.invoice-footer {
    background: #dbeef4;
    padding: 10px 0;
    text-align: center;
    margin-top: 30px;
    a {
        font-size: 10px;
        color: #0c1928 !important;
    }
}
@media print {
    .invoice-address-section {
        li {
            margin: 0;
        }
        p,
        span {
            font-size: 10px !important;
            line-height: 20px;
            max-width: 200px !important;
        }
        span {
            min-width: 110px !important;
        }
    }
    .invoice-table {
        thead {
            th {
                font-size: 10px;
            }
        }
        tbody {
            td {
                font-size: 8px;
                text-align: center;
            }
        }
    }
    .invoice-header h1 {
        text-align: right;
    }
}
/* invoice css end */
@media (max-width: 1700px) {
    .state-overview-box .icons {
        height: 70px;
        width: 70px;
        min-width: 70px;
        line-height: 70px;
        img {
            height: 40px;
        }
    }
    .state-overview-box {
        padding: 8px;
    }
    .state-overview-box h2 {
        font-size: 20px;
    }
    .state-overview-box p {
        font-size: 14px;
    }
}
@media (max-width: 1400px) {
    .erp-state-overview-wrapper {
        grid-template-columns: repeat(3, 1fr);
    }
}
@media (max-width: 1150px) {
    .table-top-form {
        display: block;
    }
    .table-top-btn-group ul {
        margin-top: 20px;
    }
    .grid-5,
    .grid-3,
    .grid-4 {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    .table-top-form .form-control {
        margin: 0;
    }
    .side-bar {
        left: -100%;
    }
    .section-container {
        padding-left: 0 !important;
    }
    .header-middle {
        display: none;
    }
    .header-wrapper {
        grid-template-columns: 50% 50%;
    }
    .language-change {
        display: none;
    }
    .w-50 {
        width: 100% !important;
    }
    .side-bar {
        width: 300px !important;
        .side-bar-manu .dropdown::before {
            opacity: 1 !important;
        }
        .sidebar-icon img {
            width: 16px !important;
        }
        .side-bar-manu li a .sidebar-icon {
            left: 0 !important;
            margin-right: 10px !important;
        }
    }
    .side-bar.active {
        left: 0;
    }
    .close-btn {
        display: block;
        font-size: 20px;
        cursor: pointer;
    }
    .grid-5 {
        flex-direction: column;
        gap: 15px;
    }
}
@media (max-width: 768px) {
    .total-count-area {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
    }
    .order-summary-tab {
        margin-top: 25px;
    }
    .grid-5 {
        grid-template-columns: repeat(1, 1fr);
    }
    .table-header {
        display: block;
    }
    .table-header .button-group {
        margin-top: 20px;
    }
    .container-fluid {
        padding: 0 15px;
    }
    .add-suplier-modal-wrapper {
        flex-direction: column;
        .row {
            order: 2;
        }
        .add-profile-photo {
            order: 1;
            margin: 0 auto !important;
        }
    }
    .erp-state-overview-wrapper {
        grid-template-columns: repeat(2, 1fr);
    }
    .total-count-area .count-item {
        max-width: 100%;
    }
    .daily-transaction-between-wrapper {
        flex-direction: column;
    }
    .daily-transaction-between-wrapper .between-wrapper input {
        font-size: 12px;
    }
}
@media (max-width: 575px) {
    .erp-state-overview-wrapper {
        grid-template-columns: repeat(1, 1fr);
    }
    .voucher-big-box-wrapper {
        gap: 20px;
    }
}
.print-inner-page {
    display: none !important;
}
@media print {
    .table-header {
        display: none;
    }
    .side-bar,
    .main-header-section,
    .print-btn {
        display: none;
    }
    .section-container {
        padding: 0;
        width: 700px;
    }
    .responsive-table {
        overflow: visible !important;
        text-align: center !important;
    }
    .print-inner-page {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        flex-direction: column;
    }
    .daily-transaction-between-wrapper,
    .total-count-area {
        display: none;
    }
    .A4-paper {
        max-width: 100% !important;
        margin: 0 auto !important;
    }
}

/* new invoice css start */
.erp-new-invice {
    max-width: 800px;
    margin: 0 auto;
    .table-header {
        h3 {
            strong {
                color: #000;
                font-size: 25px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                font-family: "Lato", sans-serif;
            }
        }
        p {
            color: #000;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            font-family: "Lato", sans-serif;
        }
    }
    .bill-invoice-wrp {
        text-align: center;
        margin: 15px 0;
        position: relative;
        .paking-date {
            position: absolute;
            right: 0;
            top: 0;
            color: #000;
            font-family: Lato;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-transform: uppercase;
        }
        h2 {
            color: #000;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            font-family: "Lato", sans-serif;
        }
        hr {
            margin: 0;
            max-width: 170px;
            margin: 0 auto;
            height: 2px;
            border-top: 2px solid #000;
            opacity: 1;
        }
    }
    .invice-detaisl {
        display: flex;
        font-family: "Lato", sans-serif;
        gap: 50px;
        .bill-left-side,
        .bill-right-side {
            width: 50%;
            margin-bottom: 15px;
            p {
                line-height: 19px;
                color: #000;
            }
        }
        .address-items {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            strong {
                display: block;
                width: 130px;
                color: #000;
                font-size: 12px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                font-family: "Lato", sans-serif;
            }
            p {
                color: #000;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                font-family: "Lato", sans-serif;
            }
        }
    }
    .bill-right-side {
        margin-left: auto;
    }
    th {
        background: transparent;
        border: 1px solid #000;
        color: #000;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        font-family: "Lato", sans-serif;
    }
    tr {
        border: none;
    }
    td {
        color: #000;
        font-family: Lato;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        font-family: "Lato", sans-serif;
        border: none;
        border-right: 1px solid #000;
        border-left: 1px solid #000;

        p {
            color: #000;
            font-family: Lato;
            font-size: 13px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            font-family: "Lato", sans-serif;
        }
        small {
            color: #000;
            font-family: "Lato", sans-serif;
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }
    }
    table {
        .address-items {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            p,
            strong {
                font-size: 13px;
                color: #000;
                line-height: 1;
            }
        }
        hr {
            opacity: 1;
            margin: 0;
            margin-bottom: 10px;
        }
    }
    .signature {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100% !important;
        max-width: 100% !important;
        border: none;
        margin-top: 80px;
        p {
            display: inline-block;
            border-top: 1px solid #000;
            color: #000;
            font-size: 13px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            padding-top: 10px;
            font-family: "Lato", sans-serif;
        }
    }
    .table-header {
        img {
            max-width: 300px;
        }
    }
    .invoice-text {
        background: #f4efe5;
        text-align: right;
        padding-right: 100px;
        margin-bottom: 30px;
        h2 {
            color: #000;
            font-family: Lato;
            font-size: 35px;
            font-style: normal;
            font-weight: 800;
            line-height: normal;
            text-transform: uppercase;
            display: inline-block;
            padding: 0 15px;
            background: #fff;
        }
    }
}
.invoice-two {
    thead {
        th {
            background: rgba(172, 133, 55, 0.13) !important;
        }
    }
    td {
        border: 1px solid #000;
    }
    th {
        h2 {
            color: #1f1f1f;
            font-family: Lato;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
        }
    }
}
.qty-new {
    min-width: 120px;
}
.invoice-payment-details {
    h3 {
        display: inline-block;
        border-bottom: 1px solid #000;
        color: #1f1f1f;
        font-family: Lato;
        font-size: 26px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    p {
        color: #000;
        font-family: Lato;
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }
}
.new-invoice-others {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    h5 {
        max-width: 340px;
        color: #000;
        font-family: Lato;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }
}
.new-invoice-footer {
    text-align: center;
    margin-top: 40px;
    p {
        background: rgba(172, 133, 55, 0.13);
        padding: 15px 0;
        color: #000;
        font-family: Lato;
        font-size: 19px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }
}
.production-report-header {
    border: 1px solid #000;
    background: #ffe6cd;
    padding: 10px;
    color: #000;
    font-family: Lato;
    font-size: 25px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.paking-detail-table {
    td {
        border: 1px solid #000;
    }
}
.closing-balance {
    color: #000;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 80px;
}
/* new invoice css end */
