<nav class="side-bar">
    <div class="side-bar-logo">
        <a href="javascript:void(0)"><img
                src="<?php echo e(asset(get_option('company')['logo'] ?? 'assets/images/logo/backend_logo.png')); ?>"
                alt="Logo"></a>
        <button class="close-btn"><i class="fal fa-times"></i></button>
    </div>
    <div class="side-bar-manu">
        <ul>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['dashboard-read'])): ?>
                <li class="<?php echo e(Request::routeIs('dashboard') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('dashboard')); ?>" class="active"><span class="sidebar-icon"><img
                                src="<?php echo e(asset('assets/images/icons/home.svg')); ?>"
                                alt="home.svg"></span><?php echo e(__('Dashboard')); ?></a>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['orders-read', 'costings-read', 'budgets-read', 'samples-read', 'bookings-read', 'shipments-read',
                'productions-read'])): ?>
                <li
                    class="dropdown <?php echo e(Request::routeIs('bookings.index', 'cuttings.index', 'items.index', 'bookings.edit', 'orders.index', 'orders.edit', 'costings.index', 'costings.edit', 'budgets.index', 'budgets.edit', 'samples.index', 'samples.edit', 'samples.show', 'shipments.index', 'shipments.edit', 'productions.create', 'productions.index', 'productions.edit', 'order.history', 'cutting-markers.*') ? 'active' : ''); ?>">
                    <a href="#"> <span class="sidebar-icon"><img src="<?php echo e(asset('assets/images/icons/order.svg')); ?>"
                                alt=""></span>
                        <?php echo e(__('Order Management')); ?></a>
                    <ul>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('orders-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('orders.index', 'productions.create', 'order.history') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('orders.index')); ?>"><?php echo e(__('Order List')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('costings-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('cuttings.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('cuttings.index')); ?>"><?php echo e(__('Cutting List')); ?></a></li>
                        <?php endif; ?>
                       
                            <li><a class="<?php echo e(Request::routeIs('cutting-markers.*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('cutting-markers.index')); ?>"><?php echo e(__('Smart Cutting Marker')); ?></a></li>
                    
                        <li><a class="<?php echo e(Request::routeIs('items.index') ? 'active' : ''); ?>"
                                href="<?php echo e(route('items.index')); ?>"><?php echo e(__('Production')); ?></a></li>

                        
                            

                        

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('shipments-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('shipments.index', 'shipments.edit') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('shipments.index')); ?>"><?php echo e(__('Shipments List')); ?></a></li>
                        <?php endif; ?>

                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['accessories-read', 'accessory-orders-read'])): ?>
                <li
                    class="dropdown <?php echo e(Request::routeIs('accessories.index', 'accessories.edit', 'accessory-orders.index', 'accessory-orders.edit', 'units.index', 'units.edit') ? 'active' : ''); ?>">
                    <a href="#"><span class="sidebar-icon"><img
                                src="<?php echo e(asset('assets/images/icons/accessory.png')); ?>"
                                alt="item.svg"></span><?php echo e(__('Manage Inventory')); ?></a>
                    <ul>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('units-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('units.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('units.index')); ?>"><?php echo e(__('Units')); ?></a></li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('accessories-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('accessories.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('accessories.index')); ?>"><?php echo e(__('Accessory List')); ?></a></li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('accessory-orders-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('accessory-orders.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('accessory-orders.index')); ?>"><?php echo e(__('Accessories Orders')); ?></a></li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            
            <?php
                $user = auth()->user();
                $isBuyer = $user && \App\Models\Party::where('user_id', $user->id)->where('type', 'buyer')->exists();
            ?>

            <?php if($isBuyer): ?>
                <li class="dropdown <?php echo e(Request::routeIs('buyer.stock.*') ? 'active' : ''); ?>">
                    <a href="#"><span class="sidebar-icon"><img
                                src="<?php echo e(asset('assets/images/icons/accessory.png')); ?>"
                                alt="stock.svg"></span><?php echo e(__('My Stock')); ?></a>
                    <ul>
                        <li><a class="<?php echo e(Request::routeIs('buyer.stock.index') ? 'active' : ''); ?>"
                                href="<?php echo e(route('buyer.stock.index')); ?>"><?php echo e(__('Stock Dashboard')); ?></a></li>
                        <li><a class="<?php echo e(Request::routeIs('buyer.stock.create-usage') ? 'active' : ''); ?>"
                                href="<?php echo e(route('buyer.stock.create-usage')); ?>"><?php echo e(__('Record Usage')); ?></a></li>
                        <li><a class="<?php echo e(Request::routeIs('buyer.stock.usage-history') ? 'active' : ''); ?>"
                                href="<?php echo e(route('buyer.stock.usage-history')); ?>"><?php echo e(__('Usage History')); ?></a></li>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('parties-read')): ?>
                <li class="dropdown <?php echo e(Request::routeIs('admin.buyer-stock.*') ? 'active' : ''); ?>">
                    <a href="#"><span class="sidebar-icon"><img
                                src="<?php echo e(asset('assets/images/icons/accessory.png')); ?>"
                                alt="stock.svg"></span><?php echo e(__('Buyer Stock Management')); ?></a>
                    <ul>
                        <li><a class="<?php echo e(Request::routeIs('admin.buyer-stock.index') ? 'active' : ''); ?>"
                                href="<?php echo e(route('admin.buyer-stock.index')); ?>"><?php echo e(__('All Buyers Stock')); ?></a></li>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['users-read'])): ?>
                <li class="dropdown <?php echo e(Request::routeIs('users.edit') || request('users') ? 'active' : ''); ?>">
                    <a href="#"><span class="sidebar-icon"><img src="<?php echo e(asset('assets/images/icons/user.svg')); ?>"
                                alt="user.svg"></span>
                        <?php echo e(__('User Management')); ?> </a>
                    <ul>
                        <li><a class="<?php echo e(request('users') == 'admin' ? 'active' : ''); ?>"
                                href="<?php echo e(route('users.index', ['users' => 'admin'])); ?>"><?php echo e(__('Admin')); ?></a></li>

                        <li><a class="<?php echo e(request('users') == 'buyer' ? 'active' : ''); ?>"
                                href="<?php echo e(route('users.index', ['users' => 'buyer'])); ?>"><?php echo e(__('Buyer')); ?></a></li>

                        <li><a class="<?php echo e(request('users') == 'merchandiser' ? 'active' : ''); ?>"
                                href="<?php echo e(route('users.index', ['users' => 'merchandiser'])); ?>"><?php echo e(__('Merchandiser')); ?></a>
                        </li>

                        <li><a class="<?php echo e(request('users') == 'commercial' ? 'active' : ''); ?>"
                                href="<?php echo e(route('users.index', ['users' => 'commercial'])); ?>"><?php echo e(__('Commercial')); ?></a>
                        </li>

                        <li><a class="<?php echo e(request('users') == 'accountant' ? 'active' : ''); ?>"
                                href="<?php echo e(route('users.index', ['users' => 'accountant'])); ?>"><?php echo e(__('Accountant')); ?></a>
                        </li>

                        <li><a class="<?php echo e(request('users') == 'production' ? 'active' : ''); ?>"
                                href="<?php echo e(route('users.index', ['users' => 'production'])); ?>"><?php echo e(__('Production')); ?></a>
                        </li>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['banks-read', 'cashes-read', 'cheques-read', 'parties-read', 'dues-read', 'party-ledger-read',
                'income-read', 'expense-read', 'credit-vouchers-read', 'debit-vouchers-read', 'debit-vouchers.create',
                'transactions-read'])): ?>

                <li
                    class="dropdown <?php echo e((!request('parties-type') && Request::routeIs('income.index', 'income.edit', 'expense.index', 'expense.edit', 'credit-vouchers.index', 'credit-vouchers.create', 'credit-vouchers.edit', 'debit-vouchers.index', 'debit-vouchers.edit', 'debit-vouchers.create', 'banks.index', 'cashes.index', 'cashes.edit', 'parties.create', 'cheques.index', 'cheques.edit', 'transactions.index', 'transaction.daily', 'transfers.index', 'transfers.edit', 'party-ledger.index', 'reports.cashbooks')) || request()->has('parties') || request()->has('transfer') ? 'active' : ''); ?>">

                    <a href="#"><span class="sidebar-icon"><img src="<?php echo e(asset('assets/images/icons/Wallet.svg')); ?>"
                                alt=""></span><?php echo e(__('Accounts & Bank')); ?> </a>
                    <ul>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['banks-read', 'cashes-read', 'cheques-read'])): ?>
                            <li
                                class="dropdown inner-dropdown <?php echo e(Request::routeIs('banks.index', 'cashes.index', 'cashes.edit', 'cheques.index', 'cheques.edit', 'transfers.index', 'transfers.edit') || request('transfer') ? 'active' : ''); ?>">
                                <a href="#"><?php echo e(__('Commercial')); ?></a>
                                <ul>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('banks-read')): ?>
                                        <li><a class="<?php echo e(Request::routeIs('banks.index', 'transfers.index') || request('transfer') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('banks.index')); ?>"><?php echo e(__('Bank Accounts')); ?></a></li>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cashes-read')): ?>
                                        <li><a class="<?php echo e(Request::routeIs('cashes.index', 'cashes.edit') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('cashes.index')); ?>"><?php echo e(__('Cash in Hand')); ?></a></li>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cheques-read')): ?>
                                        <li><a class="<?php echo e(Request::routeIs('cheques.index', 'cheques.edit') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('cheques.index')); ?>" class=""><?php echo app('translator')->get('Cheques'); ?></a></li>
                                    <?php endif; ?>

                                </ul>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['parties-read', 'dues-read', 'income-read', 'expense-read', 'credit-vouchers-read',
                            'debit-vouchers-read', 'transactions-read'])): ?>
                            <li
                                class="dropdown inner-dropdown <?php echo e((!request('parties-type') && Request::routeIs('income.index', 'income.edit', 'expense.index', 'expense.edit', 'credit-vouchers.index', 'credit-vouchers.create', 'credit-vouchers.edit', 'debit-vouchers.index', 'debit-vouchers.create', 'debit-vouchers.edit', 'parties.create', 'transactions.index', 'transaction.daily', 'party-ledger.index', 'reports.cashbooks')) || request('parties') ? 'active' : ''); ?>">
                                <a href="#"><?php echo e(__('General')); ?></a>
                                <ul>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income-read')): ?>
                                        <li><a class="<?php echo e(Request::routeIs('income.index', 'income.edit') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('income.index')); ?>"><?php echo e(__('Income')); ?></a></li>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expense-read')): ?>
                                        <li><a class="<?php echo e(Request::routeIs('expense.index', 'expense.edit') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('expense.index')); ?>"><?php echo e(__('Expenses')); ?></a></li>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['credit-vouchers-read', 'credit-vouchers-create'])): ?>
                                        <li><a class="<?php echo e(Request::routeIs('credit-vouchers.index', 'credit-vouchers.create', 'credit-vouchers.edit') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('credit-vouchers.index')); ?>"><?php echo e(__('Credit Voucher')); ?></a></li>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['debit-vouchers-read', 'debit-vouchers.create'])): ?>
                                        <li><a class="<?php echo e(Request::routeIs('debit-vouchers.index', 'debit-vouchers.create', 'debit-vouchers.edit') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('debit-vouchers.index')); ?>"><?php echo e(__('Debit Voucher')); ?></a></li>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('transactions-read')): ?>
                                        <li><a class="<?php echo e(Request::routeIs('transactions.index') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('transactions.index')); ?>"><?php echo e(__('Monthly Transaction')); ?></a></li>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('party-ledger-read')): ?>
                                        <li><a class="<?php echo e(Request::routeIs('party-ledger.index') ? 'active' : ''); ?>"
                                                href="<?php echo e(route('party-ledger.index', ['type' => 'buyer'])); ?>"><?php echo e(__('Party Ledger')); ?></a>
                                        </li>
                                    <?php endif; ?>
                                    <li>
                                        <a class="<?php echo e(Request::routeIs('reports.cashbooks') ? 'active' : ''); ?>"
                                            href="<?php echo e(route('reports.cashbooks')); ?>"><?php echo e(__('Daily Cashbook')); ?></a>
                                    </li>
                                </ul>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('parties-read')): ?>
                <li class="dropdown <?php echo e(Request::routeIs('parties.edit') || request('parties-type') ? 'active' : ''); ?>">
                    <a href="#"><span class="sidebar-icon"><img
                                src="<?php echo e(asset('assets/images/icons/Garments-Item.svg')); ?>" alt="item.svg"></span>
                        <?php echo e(__('Party List')); ?> </a>
                    <ul>
                        <li><a class="<?php echo e(request('parties-type') == 'buyer' ? 'active' : ''); ?>"
                                href="<?php echo e(route('parties.index', ['parties-type' => 'buyer'])); ?>"><?php echo e(__('Buyers')); ?></a>
                        </li>
                        <li><a class="<?php echo e(request('parties-type') == 'supplier' ? 'active' : ''); ?>"
                                href="<?php echo e(route('parties.index', ['parties-type' => 'supplier'])); ?>"><?php echo e(__('Suppliers')); ?></a>
                        </li>
                    </ul>
                </li>
            <?php endif; ?>



            
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['employees-read', 'designations-read', 'salaries-read'])): ?>
                <li class="dropdown <?php echo e(Request::routeIs('hr.*') ? 'active' : ''); ?>">
                    <a href="#">
                        <span class="sidebar-icon">
                            <img src="<?php echo e(asset('assets/images/icons/user-roles.svg')); ?>" alt="hr.svg">
                        </span>
                        <?php echo e(__('HR Module')); ?>

                    </a>
                    <ul>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('hr.dashboard') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('hr.dashboard')); ?>">
                                    <i class="fas fa-tachometer-alt me-2"></i><?php echo e(__('HR Dashboard')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('hr.employees.*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('hr.employees.index')); ?>">
                                    <i class="fas fa-users me-2"></i><?php echo e(__('Employee Management')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('hr.attendances.*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('hr.attendances.index')); ?>">
                                    <i class="fas fa-calendar-check me-2"></i><?php echo e(__('Attendance Tracking')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('hr.attendances.report') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('hr.attendances.report')); ?>">
                                    <i class="fas fa-chart-bar me-2"></i><?php echo e(__('Attendance Reports')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('attendance.biometric-report') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('attendance.biometric-report')); ?>">
                                    <i class="fas fa-robot me-2"></i><?php echo e(__('Biometric Reports')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('hr.settings.zkteco') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('hr.settings.zkteco')); ?>">
                                    <i class="fas fa-cogs me-2"></i><?php echo e(__('Biometric Settings')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('hr.other-costs.*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('hr.other-costs.index')); ?>">
                                    <i class="fas fa-coins me-2"></i><?php echo e(__('Other Costs')); ?>

                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('employees-read')): ?>
                            <li>
                                <a class="<?php echo e(Request::routeIs('hr.reports.*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('hr.reports.attendance-summary')); ?>">
                                    <i class="fas fa-file-alt me-2"></i><?php echo e(__('HR Reports')); ?>

                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dues-read')): ?>
                <li class="dropdown <?php echo e(Request::routeIs('dues.index') ? 'active' : ''); ?>">
                    <a class="<?php echo e(Request::routeIs('dues.index') ? 'active' : ''); ?>"
                        href="<?php echo e(route('dues.index', ['type' => 'buyer'])); ?>">
                        <span class="sidebar-icon"><img src="<?php echo e(asset('assets/images/icons/dues.png')); ?>"
                                alt="item.svg"></span>
                        <?php echo e(__('Party Due List')); ?>

                    </a>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('loss-profit-read')): ?>
                <li
                    class="dropdown <?php echo e(Request::routeIs('loss-profit.index', 'loss-profit.expense', 'loss-profit.income') ? 'active' : ''); ?>">
                    <a class="<?php echo e(Request::routeIs('loss-profit.index') ? 'active' : ''); ?>"
                        href="<?php echo e(route('loss-profit.index')); ?>">
                        <span class="sidebar-icon">
                            <img src="<?php echo e(asset('assets/images/icons/loss-profit.png')); ?>" alt="item.svg">
                        </span>
                        <?php echo app('translator')->get('Loss Profit'); ?>
                    </a>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['reports-read'])): ?>
                <li
                    class="dropdown <?php echo e(Request::routeIs('reports.order','reports.accessories','reports.cutting', 'reports.production', 'reports.collections', 'reports.transactions', 'reports.payable-dues.index') || request('party_type') ? 'active' : ''); ?>">
                    <a href="#">
                        <span class="sidebar-icon">
                            <img src="<?php echo e(asset('assets/images/icons/report.png')); ?>" alt="item.svg">
                        </span>
                        <?php echo e(__('Reports')); ?>

                    </a>
                    <ul>
                        <li>
                            <a class="<?php echo e(Request::routeIs('reports.order') ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.order')); ?>"><?php echo e(__('Order')); ?></a>
                        </li>
                        <li>
                            <a class="<?php echo e(Request::routeIs('reports.cutting') ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.cutting')); ?>"><?php echo e(__('Cutting')); ?></a>
                        </li>
                                                <li>
                            <a class="<?php echo e(Request::routeIs('reports.accessories') ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.accessories')); ?>"><?php echo e(__('Accessories')); ?></a>
                        </li>
                        <li>
                            <a class="<?php echo e(Request::routeIs('reports.transactions') ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.transactions')); ?>"><?php echo e(__('Transaction')); ?></a>
                        </li>
                        <li>
                            <a class="<?php echo e(Request::routeIs('reports.production') ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.production')); ?>"><?php echo e(__('Production')); ?></a>
                        </li>
                        <li>
                            <a class="<?php echo e(Request::routeIs('reports.collections') ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.collections')); ?>"><?php echo e(__('Sales Report')); ?></a>
                        </li>
                        <li>
                            <a class="<?php echo e(Request::routeIs('reports.payable-dues.index') ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.payable-dues.index')); ?>"><?php echo e(__('Purchase Report')); ?></a>
                        </li>
                        <li>
                            <a class="<?php echo e(request('party_type') == 'buyer' ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.party-dues.index', ['party_type' => 'buyer'])); ?>"><?php echo e(__('Buyer Due')); ?></a>
                        </li>
                        <li>
                            <a class="<?php echo e(request('party_type') == 'supplier' ? 'active' : ''); ?>"
                                href="<?php echo e(route('reports.party-dues.index', ['party_type' => 'supplier'])); ?>"><?php echo e(__('Supplier Due')); ?></a>
                        </li>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['roles-read', 'permissions-read'])): ?>
                <li
                    class="dropdown <?php echo e(Request::routeIs('roles.index', 'permissions.index', 'roles.create', 'roles.edit') ? 'active' : ''); ?>">
                    <a href="#">
                        <span class="sidebar-icon"><img src="<?php echo e(asset('assets/images/icons/permission.png')); ?>"
                                alt="item.svg"></span>
                        <?php echo e(__('Roles & Permissions')); ?>

                    </a>
                    <ul>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('roles.index', 'roles.create', 'roles.edit') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('roles.index')); ?>"><?php echo e(__('Roles')); ?></a></li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('permissions.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('permissions.index')); ?>"><?php echo e(__('Permissions')); ?></a></li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['settings-read', 'currencies-read'])): ?>
                <li
                    class="dropdown <?php echo e(Request::routeIs('settings.index', 'notifications.index', 'currencies.index', 'currencies.create', 'currencies.edit', 'system-settings.index', 'production-settings.index') ? 'active' : ''); ?>">
                    <a href="#">
                        <span class="sidebar-icon"><img src="<?php echo e(asset('assets/images/icons/settings.png')); ?>"
                                alt="item.svg"></span>
                        <?php echo e(__('Settings')); ?>

                    </a>
                    <ul>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('currencies-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('currencies.index', 'currencies.create', 'currencies.edit') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('currencies.index')); ?>"><?php echo e(__('Currency')); ?></a></li>
                        <?php endif; ?>
                        <?php if(Auth::user()->role == 'superadmin'): ?>
                            <li><a class="<?php echo e(Request::routeIs('notifications.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('notifications.index')); ?>"><?php echo e(__('Notifications')); ?></a></li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings-read')): ?>
                            <li><a class="<?php echo e(Request::routeIs('system-settings.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('system-settings.index')); ?>"><?php echo e(__('System Settings')); ?></a></li>
                            <li><a class="<?php echo e(Request::routeIs('settings.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('settings.index')); ?>"><?php echo e(__('Company Settings')); ?></a></li>
                            <li><a class="<?php echo e(Request::routeIs('production-settings.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('production-settings.index')); ?>"><?php echo e(__('Production Settings')); ?></a></li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </div>
</nav>
<?php /**PATH C:\xampp\htdocs\red\resources\views/layouts/partials/side-bar.blade.php ENDPATH**/ ?>