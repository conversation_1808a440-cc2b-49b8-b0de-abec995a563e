<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProductionController;
use App\Http\Controllers\AccessoryController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PartyController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\ReportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Authentication Routes
Auth::routes();

// Dashboard Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Order Management Routes
    Route::resource('orders', OrderController::class);
    Route::get('orders/{order}/details', [OrderController::class, 'details'])->name('orders.details');
    
    // Production Management Routes
    Route::resource('productions', ProductionController::class);
    Route::get('productions/get-order', [ProductionController::class, 'getOrder'])->name('productions.get-order');
    
    // Inventory Management Routes
    Route::resource('accessories', AccessoryController::class);
    Route::resource('accessory-orders', AccessoryOrderController::class);
    
    // Shipment Management Routes
    Route::resource('shipments', ShipmentController::class);
    
    // User Management Routes
    Route::resource('users', UserController::class);
    
    // Party Management Routes
    Route::resource('parties', PartyController::class);
    
    // Employee Management Routes
    Route::resource('employees', EmployeeController::class);
    
    // Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('orders', [ReportController::class, 'orders'])->name('orders');
        Route::get('production', [ReportController::class, 'production'])->name('production');
        Route::get('accessories', [ReportController::class, 'accessories'])->name('accessories');
        Route::get('cutting', [ReportController::class, 'cutting'])->name('cutting');
    });
});
