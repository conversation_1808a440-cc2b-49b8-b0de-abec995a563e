<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="<?php echo e(__('IE=edge')); ?>">
    <meta name="viewport" content="<?php echo e(__('width=device-width, initial-scale=1.0')); ?>">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php if (! empty(trim($__env->yieldContent('title')))): ?> <?php echo $__env->yieldContent('title'); ?> | <?php endif; ?><?php echo e(config('app.name')); ?></title>
    <?php echo $__env->make('layouts.partials.css', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>
<body>

<!-- Side Bar Start -->
<?php echo $__env->make('layouts.partials.side-bar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<!-- Side Bar End -->
<div class="section-container">
    <!-- header start -->
    <?php echo $__env->make('layouts.partials.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- header end -->
    <!-- erp-state-overview-section start -->
    <?php echo $__env->yieldContent('main_content'); ?>
    <!-- erp-state-overview-section end -->
    <?php echo $__env->yieldPushContent('modal'); ?>
    <div class="">
    <footer class="container-fluid d-flex align-items-center justify-content-center justify-content-sm-between flex-wrap py-3 mt-4 ms-0 bg-white">
        <p class="mb-0 me-3"> © 2025. All Rights Reserved.</p>
        
    </footer>
    </div>
</div>

<?php echo $__env->make('layouts.partials.script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\red\resources\views/layouts/master.blade.php ENDPATH**/ ?>