//colors
$color_0: #ff0
$color_1: #000
$color_2:  silver
$color_3: #666
$color_4: #111
$color_5: #1d73a2
$color_6: #175c82
$color_7: rgba(0, 0, 0, .19)
$color_8: rgba(0, 0, 0, .23)
$color_9: #357295
$color_10: #fff
$color_11: #cacfd2
$color_12: #34a0db
$color_13: rgba(0, 0, 0, .12)
$color_14: rgba(0, 0, 0, .24)
$color_15: #2490cb
$color_16: #eee
$color_17: #222
$color_18: rgba(0, 0, 0, .16)
$color_19: #2ecc71
$color_20: #e74c3c
$color_21: #f5f5f5
$color_22: rgba(0, 0, 0, .2)

//fonts
$font_0: Ionicons
$font_1: sans-serif
$font_2: monospace
$font_3: Roboto
$font_4: Helvetica Neue
$font_5: Helvetica
$font_6: Arial
$font_7: Courier New
$font_8: Courier
$font_9: Lucida Sans Typewriter
$font_10: Lucida Typewriter

//urls
$url_0: url(https://fonts.googleapis.com/css?family=Roboto:400,300,500,700,900)
$url_1: url(../fonts/ionicons.eot?v=2.0.1)
$url_2: url(../fonts/ionicons.eot?v=2.0.1#iefix)
$url_3: url(../fonts/ionicons.ttf?v=2.0.1)
$url_4: url(../fonts/ionicons.woff?v=2.0.1)
$url_5: url(../fonts/ionicons.svg?v=2.0.1#Ionicons)
$url_6: url(../img/background.png)