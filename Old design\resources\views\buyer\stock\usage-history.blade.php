@extends('layouts.master', [
    'title' => __('Usage History')
])

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4>{{ __('Usage History') }} - {{ $party->name }}</h4>
                <div class="header-action">
                    <a href="{{ route('buyer.stock.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ __('Back to Dashboard') }}
                    </a>
                    <a href="{{ route('buyer.stock.create-usage') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {{ __('Record Usage') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Form -->
                <div class="table-top-form daily-transaction-between-wrapper mb-4">
                    <form method="GET" action="{{ route('buyer.stock.usage-history') }}" class="filter-form">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="input-wrapper">
                                    <label for="start_date">{{ __('Start Date') }}</label>
                                    <input type="date" name="start_date" id="start_date" class="form-control" 
                                           value="{{ request('start_date') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-wrapper">
                                    <label for="end_date">{{ __('End Date') }}</label>
                                    <input type="date" name="end_date" id="end_date" class="form-control" 
                                           value="{{ request('end_date') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-wrapper">
                                    <label for="style">{{ __('Style') }}</label>
                                    <input type="text" name="style" id="style" class="form-control" 
                                           placeholder="{{ __('Search by style') }}" value="{{ request('style') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-wrapper">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> {{ __('Filter') }}
                                        </button>
                                        <a href="{{ route('buyer.stock.usage-history') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> {{ __('Clear') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Usage History Table -->
                <div class="table-responsive">
                    <table class="table table-striped" id="usage-table">
                        <thead>
                            <tr>
                                <th>{{ __('Date') }}</th>
                                <th>{{ __('Style') }}</th>
                                <th>{{ __('Color') }}</th>
                                <th>{{ __('Item') }}</th>
                                <th>{{ __('Size') }}</th>
                                <th>{{ __('Qty Used') }}</th>
                                <th>{{ __('Purpose') }}</th>
                                <th>{{ __('Remarks') }}</th>
                                <th>{{ __('Recorded By') }}</th>
                                <th>{{ __('Recorded At') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($usageHistory as $usage)
                            <tr>
                                <td>{{ $usage->usage_date->format('M d, Y') }}</td>
                                <td>{{ $usage->style }}</td>
                                <td>{{ $usage->color ?? '-' }}</td>
                                <td>{{ $usage->item ?? '-' }}</td>
                                <td>{{ $usage->size ?? '-' }}</td>
                                <td>
                                      {{ $usage->qty_used }}
                                </td>
                                <td>{{ $usage->purpose ?? '-' }}</td>
                                <td>
                                    @if($usage->remarks)
                                        <span class="text-truncate" style="max-width: 150px;" 
                                              title="{{ $usage->remarks }}">
                                            {{ Str::limit($usage->remarks, 30) }}
                                        </span>
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>{{ $usage->user->name ?? '-' }}</td>
                                <td>{{ $usage->created_at->format('M d, Y H:i') }}</td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center">{{ __('No usage records found') }}</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($usageHistory->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $usageHistory->appends(request()->query())->links() }}
                </div>
                @endif

                <!-- Summary -->
                @if($usageHistory->count() > 0)
                <div class="mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>{{ __('Summary') }}</h6>
                                    <p class="mb-1">
                                        <strong>{{ __('Total Records') }}:</strong> {{ $usageHistory->total() }}
                                    </p>
                                    <p class="mb-0">
                                        <strong>{{ __('Total Quantity Used') }}:</strong> 
                                        {{ $usageHistory->sum('qty_used') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    $('#usage-table').DataTable({
        "pageLength": 25,
        "order": [[ 0, "desc" ]], // Sort by date descending
        "columnDefs": [
            {
                "targets": [5], // Quantity column
                "type": "num"
            },
            {
                "targets": [0, 9], // Date columns
                "type": "date"
            }
        ],
        "searching": false, // Disable built-in search since we have custom filters
        "paging": false // Disable DataTables pagination since we use Laravel pagination
    });
});
</script>
@endpush
