@extends('layouts.master', [
    'title' => __('Advance Salaries')
])

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.select2-container {
    width: 100% !important;
}
.select2-container--default .select2-selection--single {
    height: 38px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
}
</style>
@endpush

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="table-header">
            <h4>{{ __('Advance Salaries') }}</h4>
            <div class="header-actions">
                @can('salaries-create')
                    <a href="{{ route('advance-salaries.create') }}" class="add-order-btn rounded-2">
                        <i class="fas fa-plus-circle"></i> {{ __('Create Advance') }}
                    </a>
                    <a href="{{ route('salaries.index') }}" class="add-order-btn rounded-2 btn-secondary">
                        <i class="fas fa-list"></i> {{ __('All Salaries') }}
                    </a>
                @endcan
            </div>
        </div>

        <!-- Filter Section -->
        <div class="table-top-form daily-transaction-between-wrapper mb-4">
            <form method="GET" action="{{ route('advance-salaries.index') }}" class="filter-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-wrapper">
                            <label for="employee_id">{{ __('Employee') }}</label>
                            <select name="employee_id" id="employee_id" class="form-control select2-filter">
                                <option value="">{{ __('All Employees') }}</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" {{ request('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->employee_id }} - {{ $employee->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-wrapper">
                            <label for="year">{{ __('Year') }}</label>
                            <select name="year" id="year" class="form-control">
                                <option value="">{{ __('All Years') }}</option>
                                @for($year = date('Y'); $year >= date('Y') - 2; $year--)
                                    <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-wrapper">
                            <label for="month">{{ __('Month') }}</label>
                            <select name="month" id="month" class="form-control">
                                <option value="">{{ __('All Months') }}</option>
                                @for($month = 1; $month <= 12; $month++)
                                    <option value="{{ $month }}" {{ request('month') == $month ? 'selected' : '' }}>
                                        {{ \Carbon\Carbon::create()->month($month)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="input-wrapper">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('Filter') }}
                                </button>
                                <a href="{{ route('advance-salaries.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> {{ __('Clear') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Advance Salary Table -->
        <div class="responsive-table">
            <table class="table" id="erp-table">
                <thead>
                    <tr>
                        <th>{{ __('S/N') }}</th>
                        <th>{{ __('Employee') }}</th>
                        <th>{{ __('Period') }}</th>
                        <th>{{ __('Base Salary') }}</th>
                        <th>{{ __('Advance Amount') }}</th>
                        <th>{{ __('Payment Date') }}</th>
                        <th>{{ __('Bank') }}</th>
                        <th>{{ __('Status') }}</th>
                        <th class="print-d-none">{{ __('Actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($advances as $advance)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>
                                <div>
                                    <strong>{{ $advance->employee->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $advance->employee->employee_id }}</small>
                                </div>
                            </td>
                            <td>{{ $advance->salary_period }}</td>
                            <td>{{ currency_format($advance->base_salary) }}</td>
                            <td>
                                <strong class="text-warning">{{ currency_format($advance->advance_amount) }}</strong>
                            </td>
                            <td>{{ $advance->payment_date ? $advance->payment_date->format('M d, Y') : '-' }}</td>
                            <td>{{ $advance->bank ? $advance->bank->bank_name : __('Cash') }}</td>
                            <td>
                                <span class="badge bg-info">{{ __('Advance') }}</span>
                            </td>
                            <td class="print-d-none">
                                <div class="dropdown table-action">
                                    <button type="button" data-bs-toggle="dropdown">
                                        <i class="far fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a href="{{ route('advance-salaries.show', $advance) }}" class="dropdown-item">
                                                <i class="far fa-eye"></i> {{ __('View Details') }}
                                            </a>
                                        </li>
                                        @can('salaries-update')
                                            <li>
                                                <a href="{{ route('advance-salaries.edit', $advance) }}" class="dropdown-item">
                                                    <i class="far fa-edit"></i> {{ __('Edit') }}
                                                </a>
                                            </li>
                                            <li>
                                                <button type="button" class="dropdown-item convert-advance-btn" 
                                                        data-salary-id="{{ $advance->id }}">
                                                    <i class="far fa-exchange-alt"></i> {{ __('Convert to Regular') }}
                                                </button>
                                            </li>
                                        @endcan
                                        @can('salaries-delete')
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button type="button" class="dropdown-item text-danger delete-btn" 
                                                        data-action="{{ route('advance-salaries.destroy', $advance) }}">
                                                    <i class="far fa-trash-alt"></i> {{ __('Delete') }}
                                                </button>
                                            </li>
                                        @endcan
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="text-center">{{ __('No advance salary records found') }}</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>

            <!-- Pagination -->
            @if($advances->hasPages())
                <nav>
                    <ul class="pagination">
                        <li class="page-item">{{ $advances->appends(request()->query())->links() }}</li>
                    </ul>
                </nav>
            @endif
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for employee filter
    $('.select2-filter').select2({
        placeholder: '{{ __("Search employees...") }}',
        allowClear: true,
        width: '100%'
    });

    // Delete functionality
    $(document).on('click', '.delete-btn', function() {
        const action = $(this).data('action');
        
        $.confirm({
            title: '{{ __("Are you sure?") }}',
            content: '{{ __("This action cannot be undone. The advance salary record will be permanently deleted.") }}',
            icon: 'fas fa-exclamation-triangle',
            theme: 'modern',
            closeIcon: true,
            animation: 'scale',
            type: 'red',
            buttons: {
                confirm: {
                    btnClass: 'btn-red',
                    text: '{{ __("Yes, Delete") }}',
                    action: function() {
                        $.ajax({
                            url: action,
                            method: 'DELETE',
                            data: {
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (response.message) {
                                    toastr.success(response.message);
                                }
                                if (response.redirect) {
                                    window.location.href = response.redirect;
                                } else {
                                    location.reload();
                                }
                            },
                            error: function(xhr) {
                                const response = xhr.responseJSON;
                                if (response && response.message) {
                                    toastr.error(response.message);
                                } else {
                                    toastr.error('{{ __("An error occurred while deleting the advance salary record") }}');
                                }
                            }
                        });
                    }
                },
                cancel: {
                    text: '{{ __("Cancel") }}',
                    action: function() {
                        // Do nothing
                    }
                }
            }
        });
    });

    // Convert advance functionality
    $('.convert-advance-btn').click(function() {
        const salaryId = $(this).data('salary-id');
        
        Swal.fire({
            title: '{{ __("Convert Advance to Regular Salary") }}',
            html: `
                <div class="row">
                    <div class="col-md-6">
                        <label>{{ __("Attendance Days") }}</label>
                        <input type="number" id="attendance_days" class="form-control" min="0" max="26" value="26">
                    </div>
                    <div class="col-md-6">
                        <label>{{ __("Overtime Hours") }}</label>
                        <input type="number" id="overtime_hours" class="form-control" min="0" step="0.5" value="0">
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '{{ __("Convert") }}',
            cancelButtonText: '{{ __("Cancel") }}',
            preConfirm: () => {
                const attendanceDays = document.getElementById('attendance_days').value;
                const overtimeHours = document.getElementById('overtime_hours').value;
                
                if (!attendanceDays || attendanceDays < 0 || attendanceDays > 26) {
                    Swal.showValidationMessage('{{ __("Please enter valid attendance days (0-26)") }}');
                    return false;
                }
                
                return {
                    attendance_days: attendanceDays,
                    overtime_hours: overtimeHours
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/advance-salaries/${salaryId}/convert`,
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        attendance_days: result.value.attendance_days,
                        overtime_hours: result.value.overtime_hours
                    },
                    success: function(response) {
                        toastr.success(response.message);
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        toastr.error(response.message || '{{ __("An error occurred") }}');
                    }
                });
            }
        });
    });
});
</script>
@endpush
