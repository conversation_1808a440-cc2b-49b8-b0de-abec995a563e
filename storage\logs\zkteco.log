[2025-07-25 19:31:53] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:32:37] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:34:33] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:36:54] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:37:00] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:38:49] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:38:49] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:38:49] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:38:49] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:38:49] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:38:49] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:38:53] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:38:53] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:38:57] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:38:57] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:39:19] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:39:19] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:39:42] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 19:39:42] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:39:49] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:39:49] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:40:11] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 19:40:12] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:40:38] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 19:40:38] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:45:18] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:45:18] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:45:18] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:45:18] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:45:18] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:45:18] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:46:35] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:46:35] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:55:27] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:55:50] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:55:50] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:55:50] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:55:50] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:55:50] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:55:50] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:55:50] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:55:53] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:55:53] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:55:53] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:55:56] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:55:56] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 19:55:56] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:55:59] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:56:06] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:56:06] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 19:56:06] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:56:16] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:56:16] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:56:16] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:56:23] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:56:26] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:56:26] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:56:26] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:58:42] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:58:47] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 19:58:47] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 19:58:47] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 19:58:52] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 20:56:14] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 20:56:14] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 20:56:14] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 20:56:14] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 20:56:14] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 20:56:14] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 20:56:14] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 20:56:19] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 20:56:19] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 20:56:19] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 20:56:21] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 20:56:21] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 20:56:21] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:10] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:04:10] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:04:10] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:13] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:04:13] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 21:04:13] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:15] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:04:15] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:04:15] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:19] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:04:19] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:04:19] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:19] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:04:19] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:19] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:04:19] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:22] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:04:22] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:04:22] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:25] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:04:25] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:04:25] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:04:29] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:06:17] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:06:17] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:07:00] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:07:04] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:07:09] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:07:09] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:07:34] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:07:35] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 21:07:52] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:07:52] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 21:08:12] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:08:12] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:08:19] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:08:20] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:08:36] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Failed to connect to device"} 
[2025-07-25 21:08:53] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Failed to connect to device"} 
[2025-07-25 21:09:06] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:08] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:10] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:15] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:15] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 21:09:16] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:16] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:09:21] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:09:28] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:28] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:09:30] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:31] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:09:41] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:41] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:09:49] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:09:49] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:10:19] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Failed to connect to device"} 
[2025-07-25 21:10:31] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:10:31] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:10:31] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:10:31] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:10:31] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:10:42] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:10:51] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:10:58] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:10:58] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:12:01] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:12:07] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:12:07] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:13:11] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:13:16] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:13:16] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 21:13:20] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:13:20] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:14:16] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Failed to connect to device"} 
[2025-07-25 21:14:24] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:14:55] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:14:55] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:15:59] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:16:49] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:16:49] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:16:49] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:16:55] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:16:55] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:16:55] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:16:58] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:16:58] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:18:02] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:18:41] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:18:41] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:18:48] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:18:48] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:19:52] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:19:52] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:19:52] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:19:52] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:19:52] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:26:25] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:26:25] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:27:27] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:33:02] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:33:03] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:33:12] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:33:12] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:33:55] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:33:55] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:34:16] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:34:16] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:34:16] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:34:16] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:34:16] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:34:27] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:34:30] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:34:30] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:34:57] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:35:30] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:36:46] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:36:49] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:36:51] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:36:51] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:37:53] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:38:03] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:38:08] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:38:08] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:39:11] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:39:44] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:39:44] local.INFO: Fetching attendance logs {"date":"2025-07-24","machine_ip":"*************"} 
[2025-07-25 21:40:46] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-24","machine_ip":"*************","error":"Cannot connect to machine: Failed to connect to device"} 
[2025-07-25 21:44:08] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:44:08] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:45:12] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:50:48] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:50:49] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:51:49] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:54:38] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:54:38] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:54:53] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:54:53] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:55:11] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:55:11] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:55:17] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:55:17] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:55:43] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:55:43] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:55:43] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:55:43] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:55:43] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:55:48] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:55:50] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:55:50] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:55:55] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:56:19] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:56:43] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:56:43] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:56:43] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-25 21:56:52] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-25 21:56:59] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:57:01] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:57:01] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-25 21:57:18] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:57:31] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-25 21:58:06] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 00:35:13] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 00:35:13] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 00:36:28] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 00:36:28] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 00:36:28] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 00:36:28] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 00:37:20] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 00:37:20] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 00:37:54] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 00:37:54] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 00:38:56] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 00:38:56] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 00:38:56] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 00:38:56] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 00:38:56] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 10:34:44] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 10:34:44] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:35:47] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 10:35:47] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:35:47] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 10:35:47] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:35:47] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 10:36:18] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 10:36:22] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 10:36:22] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:37:25] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 10:39:41] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 10:39:41] local.INFO: Fetching attendance logs {"date":"2025-07-25","machine_ip":"*************"} 
[2025-07-26 10:40:02] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 10:40:02] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:40:42] local.ERROR: Failed to fetch attendance logs {"date":"2025-07-25","machine_ip":"*************","error":"Cannot connect to machine: Failed to connect to device"} 
[2025-07-26 10:41:02] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 10:45:42] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 10:45:42] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:46:46] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 10:46:46] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:46:46] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 10:46:46] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:46:46] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 10:56:02] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 10:56:02] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 10:57:05] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 13:41:15] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:21] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:23] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:26] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:34] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:36] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:39] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:46] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 13:41:46] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 13:42:50] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 13:42:50] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 13:42:50] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 13:42:50] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 13:42:50] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 14:06:38] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 14:06:38] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 14:07:41] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Failed to connect to device"} 
[2025-07-26 14:07:41] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 14:07:41] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 14:07:41] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-26 14:07:41] local.ERROR: ZKTeco connection failed {"ip":"*************","port":4370,"error":"Connection error: socket_sendto(): Unable to write to socket [1]: Operation not permitted"} 
[2025-07-26 14:09:47] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 14:10:04] local.INFO: Socket operations working, using real ZKTeco adapter  
[2025-07-26 14:10:04] local.INFO: Testing connection to ZKTeco device {"ip":"*************","port":4370} 
[2025-07-28 21:20:21] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:20:21] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:20:21] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:20:21] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:29:57] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:29:57] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:29:57] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:29:57] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:30:04] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:30:04] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:30:04] local.WARNING: PHP sockets extension not available, using fallback adapter  
[2025-07-28 21:30:04] local.WARNING: PHP sockets extension not available, using fallback adapter  
