@extends('layouts.blank')

@section('title')
    {{ __('Invoice') }}
@endsection

@section('main_content')
    <div class="section-container print-wrapper p-0 erp-new-invice">
        <div class="erp-table-section">
            <div class="container p-0">
                <button class="print-window theme-btn print-btn float-end"><i class="fa fa-print"></i> Print</button>
                @include('pages.invoice.header',['title' => __('Payment Receipt')])
                <div class="d-flex justify-content-between">
                    <div>
                        <p><strong>{{ __('Party Name') }}:</strong> {{ ucwords($voucher->party->name ?? '') }}</p>
                        <p><strong>{{ __('Address') }}:</strong> {{ $voucher->party->address ?? '' }}</p>
                        <p><strong>{{ __('Mobile') }}:</strong> {{ $voucher->user->phone ?? '' }}</p>
                        <P> <strong>{{ __('Bill No') }}:</strong>  {{ $voucher->bill_no }}</P>
                    </div>
                    <div>
                        <p><strong>{{ __('Received By') }}: {{ $voucher->particulars }}</strong></p>
                        <p><strong>{{ __('Payment Method') }}:</strong> {{ ucfirst($voucher->payment_method == 'party_balance' ? 'Wallet' : $voucher->payment_method) }}</p>
                        <p><strong>{{ __('Payment Date') }}:</strong> {{ formatted_date($voucher->created_at, 'd M, Y h:i:s A') }}</p>
                    </div>
                </div>
                <table class="table commercial-invoice text-start table-bordered text-center invoice-two border-0 mt-2" id="erp-table">
                    <thead>
                    <tr>
                        <th>{{ __('Details') }}</th>
                        <th>{{ __('Received Amount') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{ $voucher->remarks }}</td>
                        <td class="fw-bold">{{ currency_format($voucher->amount ?? 0) }}</td>
                    </tr>
                    </tbody>
                </table>
                <h5><b>{{ __('Amount in word') }}: </b>{{ ucfirst(amountInWords($voucher->amount) . ' Taka') }}.</h5>
                
                    @include('pages.invoice.main-footer')
            </div>
        </div>
    </div>
@endsection
