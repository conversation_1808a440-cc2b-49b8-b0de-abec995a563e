@extends('layouts.master', [
    'title' => __('Salary Details')
])

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="table-header">
            <h4>{{ __('Salary Details') }}</h4>
            <a href="{{ route('salaries.index') }}" class="add-order-btn rounded-2">
                <i class="fas fa-arrow-left"></i> {{ __('Back to Salary List') }}
            </a>
        </div>

        <div class="order-form-section">
            <div class="row">
                <!-- Employee Information -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Employee Information') }}</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Employee ID') }}:</strong></td>
                                    <td>{{ $salary->employee->employee_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Name') }}:</strong></td>
                                    <td>{{ $salary->employee->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Designation') }}:</strong></td>
                                    <td>{{ $salary->employee->designation->name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Join Date') }}:</strong></td>
                                    <td>{{ $salary->employee->join_date ? $salary->employee->join_date->format('M d, Y') : 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Salary Information -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Salary Information') }}</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Salary Period') }}:</strong></td>
                                    <td>{{ $salary->salary_period }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Base Salary') }}:</strong></td>
                                    <td>{{ currency_format($salary->base_salary) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Status') }}:</strong></td>
                                    <td>
                                        @if($salary->payment_status === 'pending')
                                            <span class="badge bg-warning">{{ __('Pending') }}</span>
                                        @elseif($salary->payment_status === 'paid')
                                            <span class="badge bg-success">{{ __('Paid') }}</span>
                                        @elseif($salary->payment_status === 'advance')
                                            <span class="badge bg-info">{{ __('Advance') }}</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Created By') }}:</strong></td>
                                    <td>{{ $salary->user->name }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Calculation Details -->
                <div class="col-lg-12 mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Salary Calculation') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>{{ __('Attendance Days') }}</h6>
                                        <h4 class="text-primary">{{ $salary->attendance_days }}</h4>
                                        <small>{{ __('out of 26 days') }}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>{{ __('Overtime Hours') }}</h6>
                                        <h4 class="text-info">{{ $salary->overtime_hours }}</h4>
                                        <small>{{ __('extra hours') }}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>{{ __('Calculated Salary') }}</h6>
                                        <h4 class="text-success">{{ currency_format($salary->calculated_salary) }}</h4>
                                        <small>{{ __('before advance') }}</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>{{ __('Final Amount') }}</h6>
                                        <h4 class="text-dark">{{ currency_format($salary->final_salary) }}</h4>
                                        <small>{{ __('after advance deduction') }}</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- <div class="row">
                                <div class="col-md-12">
                                    <h6>{{ __('Calculation Formula') }}</h6>
                                    <div class="bg-light p-3 rounded">
                                        <code>
                                            ({{ currency_format($salary->base_salary) }} × {{ $salary->attendance_days }} ÷ 26) + 
                                            ({{ currency_format($salary->base_salary) }} ÷ 1000 × 5 × {{ $salary->overtime_hours }}) = 
                                            {{ currency_format($salary->calculated_salary) }}
                                        </code>
                                        @if($salary->advance_amount > 0)
                                            <br><br>
                                            <code>
                                                {{ currency_format($salary->calculated_salary) }} - {{ currency_format($salary->advance_amount) }} (advance) = 
                                                {{ currency_format($salary->final_salary) }}
                                            </code>
                                        @endif
                                    </div>
                                </div>
                            </div> -->
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                @if($salary->payment_status === 'paid' || $salary->payment_status === 'advance')
                <div class="col-lg-12 mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Payment Information') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>{{ __('Payment Date') }}:</strong></p>
                                    <p>{{ $salary->payment_date ? $salary->payment_date->format('M d, Y') : 'N/A' }}</p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>{{ __('Payment Method') }}:</strong></p>
                                    <p>{{ $salary->bank ? $salary->bank->bank_name : __('Cash') }}</p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>{{ __('Amount Paid') }}:</strong></p>
                                    <p>{{ currency_format($salary->payment_status === 'advance' ? $salary->advance_amount : $salary->final_salary) }}</p>
                                </div>
                            </div>
                            @if($salary->meta && isset($salary->meta['cheque_no']))
                                <div class="row">
                                    <div class="col-md-12">
                                        <p><strong>{{ __('Cheque Number') }}:</strong> {{ $salary->meta['cheque_no'] }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endif

                <!-- Notes and Remarks -->
                @if($salary->notes || $salary->remarks)
                <div class="col-lg-12 mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Additional Information') }}</h5>
                        </div>
                        <div class="card-body">
                            @if($salary->notes)
                                <div class="mb-3">
                                    <h6>{{ __('Notes') }}</h6>
                                    <p>{{ $salary->notes }}</p>
                                </div>
                            @endif
                            @if($salary->remarks)
                                <div>
                                    <h6>{{ __('Remarks') }}</h6>
                                    <p>{{ $salary->remarks }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endif

                <!-- Action Buttons -->
                <div class="col-lg-12 mt-3">
                    <div class="text-center">
                        @can('salaries-update')
                            @if($salary->payment_status === 'pending')
                                <a href="{{ route('salaries.edit', $salary) }}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> {{ __('Edit Salary') }}
                                </a>
                                <button type="button" class="btn btn-success mark-paid-btn" data-salary-id="{{ $salary->id }}">
                                    <i class="fas fa-check"></i> {{ __('Mark as Paid') }}
                                </button>
                            @endif
                        @endcan
                        
                        <a href="{{ route('salaries.index') }}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> {{ __('Back to List') }}
                        </a>
                        
                        <button onclick="window.print()" class="btn btn-info">
                            <i class="fas fa-print"></i> {{ __('Print') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mark as Paid Modal -->
<div class="modal fade" id="markPaidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Mark Salary as Paid') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="markPaidForm" class="ajaxform">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_date">{{ __('Payment Date') }} <span class="text-danger">*</span></label>
                                <input type="date" name="payment_date" id="payment_date" class="form-control" 
                                       value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bank_id">{{ __('Bank') }}</label>
                                <select name="bank_id" id="bank_id" class="form-control">
                                    <option value="">{{ __('Cash Payment') }}</option>
                                    @foreach(\App\Models\Bank::orderBy('bank_name')->get() as $bank)
                                        <option value="{{ $bank->id }}">{{ $bank->bank_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="cheque_no">{{ __('Cheque Number') }}</label>
                        <input type="text" name="cheque_no" id="cheque_no" class="form-control" 
                               placeholder="{{ __('Enter cheque number if applicable') }}">
                    </div>
                    <div class="form-group">
                        <label for="payment_remarks">{{ __('Payment Remarks') }}</label>
                        <textarea name="remarks" id="payment_remarks" class="form-control" rows="3" 
                                  placeholder="{{ __('Additional payment notes') }}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Mark as Paid') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Mark as Paid functionality
    $('.mark-paid-btn').click(function() {
        const salaryId = $(this).data('salary-id');
        const form = $('#markPaidForm');
        form.attr('action', `/salaries/${salaryId}/mark-paid`);
        $('#markPaidModal').modal('show');
    });
});
</script>
@endpush
