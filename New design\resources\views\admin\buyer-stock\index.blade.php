@extends('layouts.master', [
    'title' => __('Buyer Stock Management')
])

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4>{{ __('Buyer Stock Management') }}</h4>
            </div>
            <div class="card-body">
                <!-- Filter Form -->
                <div class="table-top-form daily-transaction-between-wrapper mb-4">
                    <form action="{{ route('admin.buyer-stock.filter') }}" method="POST" class="filter-form" table="#buyer-stock-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-3">
                                <div class="input-wrapper">
                                    <label for="search">{{ __('Search Buyer') }}</label>
                                    <input type="text" name="search" id="search" class="form-control" 
                                           placeholder="{{ __('Buyer name or email') }}" value="{{ request('search') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="input-wrapper">
                                    <label>&nbsp;</label>
                                    <div class="form-check">
                                        <input type="checkbox" name="low_stock_only" id="low_stock_only" 
                                               class="form-check-input" value="1" {{ request('low_stock_only') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="low_stock_only">
                                            {{ __('Low Stock Only') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="input-wrapper">
                                    <label>&nbsp;</label>
                                    <div class="form-check">
                                        <input type="checkbox" name="out_of_stock_only" id="out_of_stock_only" 
                                               class="form-check-input" value="1" {{ request('out_of_stock_only') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="out_of_stock_only">
                                            {{ __('Out of Stock Only') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-wrapper">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> {{ __('Filter') }}
                                        </button>
                                        <a href="{{ route('admin.buyer-stock.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> {{ __('Clear') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Buyers Stock Table -->
                <div id="buyer-stock-data">
                    @include('admin.buyer-stock.data')
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Initialize DataTable
    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#buyers-table')) {
            $('#buyers-table').DataTable().destroy();
        }
        
        $('#buyers-table').DataTable({
            "pageLength": 25,
            "order": [[ 1, "asc" ]], // Sort by buyer name
            "columnDefs": [
                {
                    "targets": [2, 3, 4, 5, 6, 7], // Numeric columns
                    "type": "num"
                }
            ],
            "searching": false, // Use custom search
            "paging": true
        });
    }

    initializeDataTable();

    // Handle filter form submission
    $('.filter-form').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                $('#buyer-stock-data').html(response.data);
                initializeDataTable();
            },
            error: function(xhr) {
                toastr.error('{{ __("An error occurred while filtering") }}');
            }
        });
    });
});
</script>
@endpush
