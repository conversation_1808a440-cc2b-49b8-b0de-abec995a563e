@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Roboto:wght@100;300;400;500;700;900&display=swap");

html {
    overflow-x: hidden;
    padding: 0px;
    margin: 0px;
}

.bg-green-one {
    background: #04f204 !important;
}

body {
    margin: 0;
    padding: 0 !important;
    font-family: "Roboto", sans-serif;
    font-size: 14px;
    line-height: 25px;
    letter-spacing: 0px;
    word-spacing: 0px;
    word-wrap: break-word;
    background: #f9f9f9;
}

#main-wrapper {
    background: #fff;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    padding: 0;
    font-weight: 400;
    color: #000000;
}

h1 {
    font-size: 36px;
    line-height: 36px;
}

h2 {
    font-size: 28px;
    line-height: 38px;
}

h3 {
    font-size: 22px;
    line-height: 32px;
}

h4 {
    font-size: 20px;
    line-height: 30px;
}

h5 {
    font-size: 18px;
    line-height: 28px;
}

h6 {
    font-size: 16px;
    line-height: 27px;
}

p {
    font-size: 14px;
    font-weight: 400;
    margin: 0;
    padding: 0;
    line-height: 24px;
    color: #505050;
}

ul,
ol {
    margin: 0;
    padding: 0;
}

ul li,
ol li {
    list-style: none;
}

a,
button,
.btn,
ol li a,
ul li a {
    letter-spacing: 0;
    text-decoration: none;
    outline: none !important;
    cursor: pointer;
    color: #000000;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

a:hover,
.btn:hover,
ol li a:hover,
ul li a:hover {
    outline: none !important;
    text-decoration: none;
    color: #000000;
}

.nowrap.table td {
    white-space: nowrap !important;
}

.btn,
.btn:focus {
    outline: none !important;
    -webkit-box-shadow: 0 0 0 0em rgba(0, 123, 255, 0.25) !important;
    box-shadow: 0 0 0 0em rgba(0, 123, 255, 0.25) !important;
}

svg {
    fill: #000000;
    --svg-font-size: 14px;
    width: var(--svg-font-size);
    height: var(--svg-font-size);
    margin-top: -4px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

/*=====================
    3 Side Bar
=======================*/
/* sidebar menu end css */
.side-bar {
    padding: 0 25px;
    position: fixed;
    top: 0;
    left: 0;
    width: 310px;
    height: 100%;
    z-index: 10;
    overflow-y: auto;
    overflow-x: hidden;
    opacity: 1;
    visibility: visible;
    background: #000;
    -webkit-box-shadow: 0px 0px 5px rgba(45, 51, 103, 0.15);
    box-shadow: 0px 0px 5px rgba(45, 51, 103, 0.15);
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    border-radius: 4px;
}

.side-bar-logo {
    padding: 20px;
    border-bottom: 1px solid rgba(69, 74, 84, 0.4);
}

.side-bar-logo img {
    max-width: 100%;
}

.close-btn {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    font-size: 20px;
    position: absolute;
    right: 15px;
    top: 15px;
    display: none;
}

.side-bar-manu .dropdown::before {
    content: "\f105";
    font-family: "Font Awesome 5 Pro";
    font-weight: 400;
    color: #aaabae;
    position: absolute;
    right: 15px;
    font-size: 16px;
    display: block;
    z-index: 3;
    top: 10px;
}

.side-bar-manu .inner-dropdown::before {
    top: 5px !important;
}

.side-bar-manu .dropdown.active > .dropdown-menu {
    display: block;
}

.side-bar-manu .dropdown.active > .dropdown-menu .dropdown {
    background: none;
}

.side-bar-manu .dropdown.active > .dropdown-menu .dropdown.active > a,
.side-bar-manu .dropdown.active > .dropdown-menu .dropdown.active .active {
    background: #292be9;
}

.side-bar-manu .dropdown.active .active {
    background: #292be9;
}

.side-bar-manu li {
    position: relative;
    margin: 0;
    font-size: 12px;
    margin: 5px 0;
}

.side-bar-manu li a {
    position: relative;
    display: block;
    padding: 10px 20px;
    color: #fff;
    border-radius: 4px;
    z-index: 2;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 16px;
}

.side-bar-manu li a .sidebar-icon {
    display: inline-block;
    margin-right: 10px;
}

.side-bar-manu li a i {
    margin-right: 10px;
}

.side-bar-manu li:hover:before,
.side-bar-manu li.active:before {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    color: #fff;
}

.side-bar-manu li:hover > a,
.side-bar-manu li.active > a {
    background-color: #292be9;
    color: #fff !important;
}

.side-bar-manu li .dropdown-menu {
    margin-left: 20px;
    border-left: 1px solid #fff;
}

.side-bar-manu li .dropdown-menu li .dropdown-menu {
    margin-left: 0 !important;
    border: none !important;
}

.side-bar-manu
    li
    .dropdown-menu
    li
    .dropdown-menu
    .dropdown
    .dropdown-menu
    .dropdown {
    background: none !important;
    padding-left: 0 !important;
}

.side-bar-manu li .dropdown-menu li {
    padding-left: 15px;
}

.side-bar-manu li .dropdown-menu li a {
    color: #fff;
    padding: 5px 15px;
    position: relative;
}

.side-bar-manu li .dropdown-menu li a:hover,
.side-bar-manu li .dropdown-menu li a.active {
    color: #fff !important;
}

.side-bar-manu li .dropdown-menu li li:hover:before,
.side-bar-manu li .dropdown-menu li li.active:before {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
}

.side-bar-manu li .dropdown-menu li li:hover > a,
.side-bar-manu li .dropdown-menu li li.active > a {
    background-color: #292be9;
    color: #fff !important;
}

.side-bar-manu .dropdown-menu {
    padding: 0;
    border-radius: 0;
    background: transparent;
}

.side-bar-manu .dropdown.active > .dropdown-menu .dropdown {
    background: none !important;
}

.side-bar .dropdown-menu {
    position: unset;
}

.side-bar li ul {
    display: none;
}

.sidebar-icon img {
    width: 20px;
}

.side-bar.active {
    width: 80px;
}

.side-bar.active .side-bar-manu .dropdown::before {
    opacity: 0;
}

.side-bar.active .side-bar-manu li a .sidebar-icon {
    margin-right: 30px;
    position: relative;
    left: -10px;
}

.side-bar.active .sidebar-icon img {
    width: 22px;
}

.side-bar.active:hover {
    width: 300px;
}

.side-bar.active:hover .side-bar-manu .dropdown::before {
    opacity: 1;
}

.side-bar.active:hover .side-bar-manu li a .sidebar-icon {
    margin-right: 10px;
    position: relative;
    left: 0px;
}

.side-bar.active:hover .sidebar-icon img {
    width: 16px;
}

.section-container {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.section-container.active {
    padding-left: 80px;
}

.cards {
    border: 0;
    box-shadow: 0px 4px 24px 0px #e4c3c30d;
    border-radius: 12px;
    min-height: 100vh;
    background-color: white;
}
.table-header {
    padding: 3px 16px 12px !important;
}
.table-header.justify-content-end.border-0.p-0 {
    padding: 10px 16px 0 !important;
}
/* sidebar menu end css */
/* header  start css */
.header-wrapper {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 33% 33% 33%;
    grid-template-columns: 33% 33% 33%;
}

.main-header-section {
    padding: 10px 30px;
    background: #fff;
    -webkit-box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
    box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
}

.main-header-section .profile-info img {
    height: 50px;
    width: 50px;
    min-width: 50px;
    border-radius: 50%;
}

.main-header-section .profile-info img span {
    font-size: 16px;
}

.main-header-section .profile-info .dropdown-menu {
    background: #fff;
    -webkit-box-shadow: 0px 0px 7px -5px rgba(0, 0, 0, 0.75);
    box-shadow: 0px 0px 7px -5px rgba(0, 0, 0, 0.75);
    min-width: 160px;
}

.main-header-section .profile-info .dropdown-menu li {
    border-bottom: 1px solid #454a54;
    font-size: 12px;
    padding: 0;
    display: block;
    border-bottom: 1px solid #ccc;
}

.main-header-section .profile-info .dropdown-menu li a {
    display: block;
    color: #000;
    padding: 8px 10px;
}

.main-header-section .profile-info .dropdown-menu li a i {
    margin-right: 10px;
}

.main-header-section .profile-info .dropdown-menu li a:hover {
    color: #292be9;
}

.order-form-section{
    padding: 14px;
}

.main-header-section .dropdown-menu {
    top: 60% !important;
    border: none;
    border-radius: 2px !important;
    padding: 0;
    min-width: 220px;
}

.header-left {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.header-right {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}

.language-change {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.language-change img {
    height: 30px;
    width: 30px;
    border-radius: 50%;
}

.language-change .nice-select {
    background: none;
    border: none;
}

.language-change .nice-select:after {
    border-color: #000;
    height: 7px;
    top: 46%;
    width: 7px;
}

.header-calender {
    height: 45px;
    width: 45px;
    line-height: 45px;
    text-align: center;
    cursor: pointer;
}

.notifications.dropdown > a {
    position: relative;
    display: block;
    padding: 0;
    color: #737881;
    height: 45px;
    width: 45px;
    text-align: center;
    line-height: 45px;
}

.notifications.dropdown > a.show {
    background-color: #eee;
    -webkit-border-radius: 3px 3px 0 0;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px 3px 0 0;
    -moz-background-clip: padding;
    border-radius: 3px 3px 0 0;
    background-clip: padding-box;
}

.bg-info {
    background: #21a9e1;
    color: #fff;
}

.text-red {
    color: red !important;
}

.notifications {
    margin-left: 10px;
}

.notifications .dropdown-toggleer {
    position: relative;
}

.notifications .dropdown-toggleer span {
    display: block;
    height: 17px;
    width: 17px;
    border-radius: 50%;
    line-height: 17px;
    text-align: center;
    font-size: 10px;
    position: absolute;
    top: 2px;
    right: 6px;
}

.notifications .dropdown-menu {
    top: 0 !important;
    background: #eee;
    min-width: 370px;
}

.notifications ul li a {
    padding: 10px 20px;
    display: block;
    border-top: 1px solid #ddd;
}

.notifications ul li a span,
.notifications ul li a strong {
    display: block;
    font-size: 12px;
}

.notifications ul li a span {
    color: #888888;
    line-height: 1;
    margin-top: 5px;
}

.notification-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 10px;
    background: #eee;
}

.notification-header p {
    font-size: 12px;
}

.notification-header a {
    font-size: 12px;
}

.bg-red {
    background: #ee4749;
    color: #fff;
}

.notification-footer {
    background: #fff;
    padding: 10px;
    line-height: 1;
}

.notification-footer a {
    font-size: 12px;
    line-height: 1;
}

.msg-items {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.msg-items .content {
    width: 100%;
}

.msg-items .content strong {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.msg-items .content strong span {
    margin: 0;
    margin-left: 10px;
    font-weight: 400;
}

.msg-items img {
    height: 50px;
    width: 50px;
    min-width: 50px;
    border-radius: 50%;
    margin-left: 15px;
}

.bg-yellow {
    background: yellow;
}

.sidebar-opner {
    cursor: pointer;
    font-size: 20px;
    color: #000;
    margin-right: 20px;
}

.header-middle input {
    background: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

.search-btn {
    background: #292be9 !important;
    color: #fff !important;
}

/* header  end css */
.section-container {
    padding-left: 300px;
    /* padding-bottom: 80px; */
}

.erp-state-overview-section {
    padding-top: 30px;
}

.erp-state-overview-wrapper {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 30px 1fr 30px 1fr 30px 1fr 30px 1fr;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
}

.container-fluid {
    padding: 0 27px;
}

.state-overview-box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: #ffffff;
    -webkit-box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.24),
        0px 3px 8px -1px rgba(71, 50, 50, 0.05);
    box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.24),
        0px 3px 8px -1px rgba(71, 50, 50, 0.05);
    border-radius: 25px;
    padding: 10px;
    gap: 15px;
}

.state-overview-box .icons {
    height: 100px;
    width: 100px;
    min-width: 100px;
    background: rgba(255, 16, 188, 0.1);
    border-radius: 17px;
    text-align: center;
    line-height: 100px;
}

.state-overview-box h2 {
    font-weight: 500;
    font-size: 30px;
    line-height: 35px;
    color: #0c1928;
}

.state-overview-box p {
    font-weight: 400;
    font-size: 18px;
    line-height: 21px;
    color: #555e69;
}

.state-overview-box:nth-child(2) .icons {
    background: rgba(255, 206, 42, 0.1);
}

.state-overview-box:nth-child(3) .icons {
    background: rgba(23, 218, 148, 0.1);
}

.state-overview-box:nth-child(4) .icons {
    background: rgba(23, 218, 148, 0.1);
}

.state-overview-box:nth-child(5) .icons {
    background: rgba(3, 169, 244, 0.1);
}

.erp-graph-box {
    margin-top: 30px;
    background: #ffffff;
    -webkit-box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.24),
        0px 3px 8px -1px rgba(71, 50, 50, 0.05);
    box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.24),
        0px 3px 8px -1px rgba(71, 50, 50, 0.05);
    border-radius: 25px;
}

.erp-graph-box .graph-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: rgba(255, 73, 128, 0.1);
    -webkit-box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
    box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
    border-radius: 25px 25px 0px 0px;
    padding: 15px 20px;
}

.erp-graph-box .graph-header h4 {
    font-weight: 400;
    font-size: 16px;
    line-height: 21px;
    color: #0c1928;
}

.erp-graph-box .graph-header .nice-select {
    height: auto;
    line-height: 1;
    background: none;
    border: none;
    font-size: 16px;
    color: #0c1928;
}

.erp-graph-box .graph-header .nice-select:after {
    height: 7px;
    width: 7px;
    border-color: #000;
    top: 45%;
}

.erp-graph-box .erp-box-content {
    padding: 15px;
    overflow: auto;
}

.yearly-status .graph-header {
    background: rgba(175, 73, 255, 0.1);
}

.top-customer .graph-header {
    background: rgba(209, 255, 26, 0.2);
    -webkit-box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
    box-shadow: 8px 10px 80px rgba(0, 15, 55, 0.04);
}

.new-order .graph-header {
    background: rgba(14, 180, 255, 0.1);
    color: #0eb4ff;
}

.table-profile {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.table-profile .user-img {
    margin-right: 10px;
}

.table-profile .user-img img {
    height: 40px;
    width: 40px;
    min-width: 40px;
    border-radius: 50%;
}

.table-profile span {
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    color: #03a9f4;
    display: block;
    opacity: 0.6;
}

.table-profile strong {
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: #0c1928;
    opacity: 0.7;
}

.table-profile-order .user-info-left span {
    color: #03a9f4;
}

.table-profile-order .user-info-left {
    text-align: left !important;
}

.table-profile-order .user-info-right {
    text-align: right !important;
}

.table-profile-order strong {
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: #0c1928;
}

.top-customer-table{
height: 280px;
}
.footer-text{
    color: #292BE9;
}

.top-customer-table td {
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    color: #0c1928;
    text-align: center;
    vertical-align: middle;
    padding: 5px;
    border: none;
}

.top-customer-table a {
    color: #03a9f4;
}

.status-btn {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    padding: 8px 20px;
    border-radius: 8px;
    border: none;
    line-height: 1;
    border-radius: 8px;
    min-width: 90px;
}

.pending {
    background: rgba(255, 164, 28, 0.2) !important;
    color: #ffa41c !important;
}

.aproved {
    background: rgba(37, 212, 169, 0.1) !important;
    color: #25d4a9 !important;
}

/* table css start css */
.erp-table-section {
    padding-top: 30px;
    font-family: "Manrope", sans-serif;
}

.table-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(126, 126, 126, 0.3);
}

.table-header h4 {
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    text-transform: capitalize;
    color: #191919;
    font-family: "Manrope", sans-serif;
}

.table-header h3 {
    font-weight: 600;
}

.table-header a {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #7e7e7e;
    padding: 5px 15px;
}

.table-header .button-group {
    border-bottom: 1px solid #292be9;
}

.table-header .button-group a {
    background: none !important;
    color: #7e7e7e !important;
    border-radius: 6px 6px 0px 0px;
}

.table-header .button-group a.active {
    background: #292be9 !important;
    color: #fff !important;
}

.add-order-btn {
    background: #292be9 !important;
    color: #fff !important;
    border-radius: 6px 6px 0px 0px;
}

.grid-5 {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.due-list-filter {
    display: flex;
}

.table-top-form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.table-top-form form {
    max-width: 1100px;
}

.table-top-form .form-control {
    height: 32px;
    line-height: 1;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

.table-top-form .nice-select .list {
    width: 100%;
}

.input-wrapper {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100%;
}

.input-wrapper > .form-control,
.input-wrapper .form-select {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    margin: 0 10px;
    height: 38px;
    border: 1px solid #d8d8d8 !important;
    border-radius: 8px;
}

.input-wrapper .position-absolute {
    top: 50%;
    transform: translateY(-50%) !important;
    right: 7%;
    height: 30px;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.table-top-btn-group ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 10px;
}

.table-top-btn-group ul a {
    height: 24px;
    width: 24px;
    background: rgba(0, 165, 81, 0.1);
    border-radius: 4px;
    text-align: center;
    display: block;
    padding: 0;
}

.table-top-btn-group ul li:nth-child(1) a {
    background: rgba(255, 132, 0, 0.1);
}

.table-top-btn-group ul li:nth-child(4) a {
    color: #544d4d;
    background: rgba(241, 60, 60, 0.1);
}

.responsive-table {
    overflow: auto;
    min-height: 320px;
}

.table {
    border: 1px solid rgba(31, 31, 31, 0.1);
}

.table th {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    text-transform: capitalize;
    color: #191919;
    background: rgba(31, 31, 31, 0.05);
}

.table th,
.table td {
    border-bottom: 1px solid rgba(31, 31, 31, 0.1);
    /* white-space: nowrap; */
    vertical-align: middle;
    text-align: center;
    white-space: nowrap;
}

.table td {
    font-weight: 400;
    font-size: 12px;
    line-height: 22px;
    color: #7e7e7e;
    text-align: center;
}

.table-two td {
    font-weight: 500;
    font-size: 10px;
    line-height: 22px;
    text-transform: capitalize;
    color: #191919;
    vertical-align: middle !important;
}

.table-two th,
.table-two td {
    border: 1px solid rgba(0, 0, 0, 0.5);
    background: none;
}

.table-two th {
    font-weight: 500;
    font-size: 14px !important;
    line-height: 22px;
    text-transform: capitalize;
    color: #191919;
}

.table-two thead td {
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    text-transform: capitalize;
    color: #191919;
    white-space: pre-wrap;
    vertical-align: bottom;
}

.loss-profit-table td,
.loss-profit-table th {
    min-width: 100px !important;
    font-size: 12px;
}

/* .loss-profit-table .big-width-th-td {
    min-width: 230px !important;
} */

.table-img {
    height: 25px;
    max-width: 100%;
}

.table-action button {
    font-size: 22px;
    background: none;
    border: none;
    outline: none;
    text-align: center;
    display: block;
    width: 100%;
}

.table-action .dropdown-menu {
    min-width: 200px;
    padding: 0;
    background: #ffffff;
    -webkit-box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    padding: 10px;
    padding: 0;
}

.table-action .dropdown-menu a {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: #7e7e7e;
    display: block;
    padding: 10px 15px;
}

.table-action .dropdown-menu a i {
    margin-right: 7px;
}

.table-action .dropdown-menu a:hover {
    background: #eee;
}

.pagination {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}

.pagination .page-link {
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    color: #7e7e7e;
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    background: none;
}

.pagination .page-item.active .page-link {
    color: #292be9;
}

.pagination .page-link.active,
.pagination .page-link:hover {
    color: #292be9;
    background: none;
}

.ui-widget.ui-widget-content {
    z-index: 3 !important;
}

.order-form-section label {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #191919;
    background: #fff;
    position: relative;
    left: 20px;
    top: 11px;
    padding: 0 10px;
    z-index: 2;
    line-height: 1;
}

.order-form-section .remove-position {
    position: static !important;
}

.order-form-section .form-control {
    -webkit-box-shadow: none;
    box-shadow: none;
    background: #ffffff;
    border: 1px solid #e4e5e7;
    border-radius: 6px;
    padding: 12px 15px;
    font-weight: 400;
}

.order-form-section .form-control::-webkit-input-placeholder {
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #7e7e7e;
}

.order-form-section .form-control::-moz-placeholder {
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #7e7e7e;
}

.order-form-section .form-control:-ms-input-placeholder {
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #7e7e7e;
}

.order-form-section .form-control::-ms-input-placeholder {
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #7e7e7e;
}

.order-form-section .form-control::placeholder {
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #7e7e7e;
}

.order-form-section .table-select {
    height: 50px;
    line-height: 24px;
}

.order-form-section h6 {
    font-weight: 600;
}

/* print-blade-image css start */
.print-blade-image {
    border: 1px solid rgba(31, 31, 31, 0.1);
    padding: 0px !important;
}

.print-blade-image .upload-img {
    margin-top: 3px;
    top: 0 !important;
    left: 0 !important;
    background: #ffffff;
    border-top: 1px solid rgba(31, 31, 31, 0.1);
    display: block;
    height: 128px;
    cursor: pointer;
    position: relative;
    text-align: center;
}

.print-blade-image .upload-img i {
    position: absolute;
    font-size: 30px;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.print-blade-image .upload-img p {
    position: absolute;
    bottom: 15px;
    left: 0;
    width: 100%;
    font-weight: 400;
    line-height: 22px;
    text-transform: capitalize;
    color: #555e69;
}

.print-blade-image .upload-img .image-thumb {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

/* order-management-image css start */
.order-management-image .upload-img {
    margin-top: 5px;
    background: #ffffff;
    border: 2px solid rgba(126, 126, 126, 0.4);
    border-radius: 10px;
    height: 206px;
    cursor: pointer;
    position: relative;
    display: block;
}

.order-management-image .budget-upload-img {
    height: 135px !important;
}

.order-management-image .upload-img img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.add-color-btn {
    border: 1px solid #292be9;
    border-radius: 3px;
    padding: 2px 8px;
    font-size: 10px;
    color: #292be9;
    cursor: pointer;
    display: inline-block;
    line-height: 1.6;
}

input[type="date"] {
    min-width: 125px !important;
}

.theme-btn {
    min-width: 210px;
    background: #292be9;
    border-radius: 6px;
    color: #fff;
    border: none;
    padding: 10px;
    display: inline-block;
    text-align: center;
}

.border-btn {
    border: 1px solid #7e7e7e;
    border-radius: 6px;
    background: none;
    color: #7e7e7e;
}

.table-title {
    text-align: center;
    background: #ffe6cc;
    border-radius: 5px 5px 0px 0px;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    text-transform: capitalize;
    color: #191919;
    padding: 5px 10px;
}

.status-btn {
    display: inline-block;
    font-weight: 500;
    font-size: 13px;
    line-height: 18px;
    text-transform: capitalize;
    padding: 3px 10px;
    border-radius: 4px;
    min-width: 72px;
}

.pending2 {
    color: #555e69;
    background: rgba(85, 94, 105, 0.1);
}

.complete {
    background: rgba(6, 178, 75, 0.1);
    color: #06b34b;
}

.canceled {
    background: rgba(255, 98, 125, 0.1);
    color: #ff627d;
}

.select-tow {
    height: 36px;
    line-height: 16px;
    width: 100%;
    font-weight: 600;
}

.select-tow .list {
    width: 100%;
}

.mt-20 {
    margin-top: 20px;
}

.mt-30 {
    margin-top: 30px;
}

.mb-30 {
    margin-bottom: 30px;
}

.clr-black {
    color: #191919 !important;
    font-weight: 500;
}

.clr-black td,
.clr-black th {
    color: #191919 !important;
}

b,
strong {
    color: #191919 !important;
}

.small-table tr td {
    font-family: "Manrope";
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    text-transform: capitalize;
    color: #191919;
    text-align: left;
    padding: 5px 10px;
    word-wrap: wrap;
    white-space: pre-wrap !important;
}

.small-table tr td:first-child {
    max-width: 270px;
    width: 200px;
}

.small-table tbody th {
    text-align: left;
    font-size: 12px;
}

.small-table th {
    background: none;
    word-wrap: wrap;
    white-space: pre-wrap !important;
    color: #191919;
    font-weight: 700;
    font-size: 12px;
}

.clr-gray td {
    color: #7e7e7e !important;
}

.theme-border {
    border-color: #292be9;
    color: #292be9;
}

.title-four {
    text-align: center;
    font-family: "Manrope";
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    text-transform: capitalize;
    color: #191919;
    background: rgba(31, 31, 31, 0.05);
}

.signature {
    max-width: 200px;
    margin: 0 auto;
    border-top: 1px solid #000;
    text-align: center;
}

.add-suplier-wrapper {
    position: relative;
}

.add-suplier-wrapper .nice-select:after {
    right: 60px;
}

.add-suplier-wrapper .add-suplier-modal {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    height: 49px;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    color: #fff;
    background-color: #292be9;
    border: 1px solid #ced4da;
    display: flex;
    align-items: center;
}

.nice-select {
    display: flex;
    align-items: center;
}

.nice-select .current {
    padding-right: 5px;
}

.nice-select:after {
    height: 10px;
    width: 10px;
    top: 43%;
    right: 20px;
}

.modal-body {
    padding-top: 0;
}

.modal-content {
    padding-bottom: 20px;
}

.add-suplier-modal-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    gap: 30px;
}

.add-profile-photo {
    min-width: 160px;
    width: 160px;
    text-align: center;
}

.add-profile-photo-wrapper {
    position: relative;
    background: none;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    margin-top: 35px;
    max-width: 160px;
    height: 160px;
    cursor: pointer;
}

.add-profile-photo-wrapper .image-wrapper {
    height: 150px;
    width: 150px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #292be9;
}

.add-profile-photo-wrapper .image-wrapper img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.add-profile-photo-wrapper .icons {
    position: absolute;
    height: 50px;
    width: 50px;
    border-radius: 50%;
    border: 2px solid #292be9;
    text-align: center;
    line-height: 45px;
    font-size: 20px;
    color: #919699;
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.costing-price-wrapper {
    padding: 30px 0;
}

.costing-list li {
    display: block;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 15px;
}

.costing-list span {
    min-width: 165px !important;
    display: inline-block;
    font-family: "Manrope";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 18px;
    color: #191919;
}

.costing-list span:nth-child(2) {
    text-align: center;
    min-width: 40px !important;
}

.mark-style {
    background: #ffe6cc !important;
    color: #7e7e7e !important;
}

.t-shirt-thumb {
    max-width: 240px;
    height: 250px;
    border: 1px solid rgba(126, 126, 126, 0.5);
    border-radius: 6px;
    text-align: center;
    padding: 15px 25px;
    margin-left: auto;
}

.t-shirt-thumb img {
    width: 100%;
    height: 100%;
}

.table-title-three {
    background: #f4f4f4;
    border-radius: 5px 5px 0px 0px;
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: (1fr) [3];
    grid-template-columns: repeat(3, 1fr);
    padding: 5px 15px;
    margin-top: 25px;
}

.table-title-three h5 {
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    color: #191919;
}

.table-two.table-bordered {
    border-color: #fff;
}

.table-two.table-bordered td,
.table-two.table-bordered th {
    border: 1px solid rgba(126, 126, 126, 0.4);
}

.fabric-details-table td {
    min-width: 60px !important;
    white-space: wrap !important;
}

.report-production td {
    min-width: 50px !important;
    white-space: wrap !important;
    padding: 0px 20px;
}

.t-header {
    background: #f4f4f4 !important;
    color: #191919 !important;
    font-weight: 500 !important;
}

.bank-account-info {
    padding: 15px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    margin-top: 20px;
}

.bank-account-info .costing-list span {
    min-width: 130px;
}

.bank-account-info .add-order-btn {
    font-size: 14px;
    width: auto;
    margin-left: auto;
}

.title-five {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 20px 0;
    padding-bottom: 0;
}

.add-suplier-modal-wrapper .nice-select {
    line-height: 23px;
}

.add-suplier-modal-wrapper .nice-select .list {
    width: 100%;
}

.order-form-section .costing-list {
    margin: 20px auto;
}

.order-form-section .costing-list span {
    min-width: 180px;
}

.text-success {
    color: #06b34b !important;
}

.view-cheque-items {
    padding: 15px 30px;
    background: #f4f4f4;
    border-radius: 10px;
    margin-bottom: 30px;
}

.view-cheque-items .title-seven {
    font-family: "Manrope";
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    text-transform: capitalize;
    color: #191919;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(126, 126, 126, 0.3);
    margin-bottom: 15px;
}

.bank-status-list li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 7px 0;
}

.bank-status-list li span {
    font-family: "Manrope";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    display: inline-block;
    margin-left: 15px;
    text-transform: capitalize;
    color: #0c1928;
}

.bank-status-list li .w-140 {
    color: #7e7e7e;
    margin: 0;
}

.w-140 {
    width: 160px;
    display: inline-block;
    margin: 0;
}

.loan-view-modal-wrapper .bank-status-list span {
    color: #7e7e7e !important;
    font-size: 14px !important;
}

.loan-view-modal-wrapper .bank-status-list .w-140 {
    color: #0c1928 !important;
}

th table th {
    background: none !important;
    padding: 7px 0 !important;
    text-align: center;
    border: 1px solid rgba(31, 31, 31, 0.1);
}

.top-customer-table .table th,
.top-customer-table .table td {
    border: none !important;
}

.top-customer-table .table {
    border: none !important;
}

/*--------------------------------------
    - login css start
----------------------------------------*/
.mybazar-login-section {
    height: 100vh;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-image: url("../../assets/images/bg/login-bg.jpg");
    background-size: cover;
    background-position: center;
}

.mybazar-login-section .mybazar-login-wrapper {
    max-width: 550px;
    margin: 0 auto;
    width: 100%;
    position: relative;
    z-index: 2;
}

.mybazar-login-section .login-wrapper {
    background: #fff;
    -webkit-box-shadow: 0px 20px 50px rgba(12, 25, 40, 0.1);
    box-shadow: 0px 20px 50px rgba(12, 25, 40, 0.1);
    border-radius: 30px;
    padding: 30px;
}

.mybazar-login-section .login-wrapper h2 {
    text-align: center;
    font-weight: 600;
    font-size: 30px;
    line-height: 38px;
    color: #101828;
}

.mybazar-login-section .login-wrapper h2 span {
    color: #292be9;
}

.mybazar-login-section .login-wrapper h6 {
    text-align: center;
    font-weight: 400;
    font-size: 18px;
    line-height: 28px;
    color: #344054;
    margin-bottom: 20px;
}

.mybazar-login-section .login-wrapper p {
    color: #0c1928;
    opacity: 0.5;
    text-align: center;
}

.mybazar-login-section .login-wrapper .input-group {
    position: relative;
    margin: 0;
    margin-top: 10px;
    border: 1px solid #98a2b3;
    background-color: #f4f5f7;
}

.mybazar-login-section .login-wrapper .input-group span {
    position: absolute;
    top: 50%;
    left: 15px;
    z-index: 5;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
}

.mybazar-login-section .login-wrapper .input-group .hide-pass {
    position: absolute;
    top: 50%;
    left: auto;
    right: 15px;
    z-index: 5;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
}

.mybazar-login-section .login-wrapper .input-group .form-control {
    padding: 12px 15px;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding-left: 12px;
    height: 40px;
    border-radius: 0px !important;
    font-size: 14px;
}

.mybazar-login-section .login-wrapper .input-group input {
    border-left: 1px solid #98a2b3 !important;
    border: none;
    margin-left: 50px !important;
}

.mybazar-login-section .login-btn {
    display: block;
    font-size: 16px;
    color: #fff !important;
    background: #292be9;
    border-radius: 5px;
    padding: 0px 10px;
    width: 100%;
    margin-top: 20px;
    border: 1px solid transparent;
    height: 35px;
}

.mybazar-login-section .login-btn:hover,
.mybazar-login-section .login-btn:focus {
    background: #fff !important;
    color: #000 !important;
    border: 1px solid #292be9;
}

.mybazar-login-section .button-group {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: auto 10px auto 10px auto 10px auto;
    grid-template-columns: auto auto auto auto;
    gap: 10px;
}

.mybazar-login-section .forget-password {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mybazar-login-section .back-to-login {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

.mybazar-login-section .forget-password a,
.mybazar-login-section .back-to-login a {
    font-size: 16px;
    font-weight: 400;
    color: #0c1928;
    display: inline-block;
    margin: 5px;
}

.mybazar-login-section .forget-password a:hover,
.mybazar-login-section .back-to-login a:hover {
    text-decoration: underline;
}

.mybazar-login-section .login-btn-group {
    min-width: 115px !important;
    padding: 5px 0px !important;
    border-radius: 5px !important;
}

/* image upload */
.upload-img-v2 {
    height: 100%;
}

.upload-img-v2 label {
    height: 130px;
    border-radius: 5px;
    border: 1px solid var(--neutral-300, #d4d4d4);
    background: #fff;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 20px;
    cursor: pointer;
    padding: 0px;
}

.upload-img-v2 .image-height {
    height: 138px !important;
}

.upload-v4 .img-wrp {
    background: #f5f5f5 !important;
    padding: 25px;
    height: 140px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    min-width: 140px;
}

.upload-v4 .img-wrp img {
    width: 100%;
    height: auto;
    border: none;
}

.upload-img-v2 label img {
    width: 150px;
    height: 141px;
    border-radius: 5px;
    border-right: 1px solid var(--neutral-300, #d4d4d4);
}

.settings-image-upload {
    position: relative;
    margin-top: 10px;
}

.settings-image-upload .title {
    position: absolute;
    background-color: #fff;
    z-index: 3;
    left: 21px;
    top: 4px;
}

.upload-img-v2 .settings-upload-v4 {
    height: 100px;
    display: flex;
    justify-content: center;
    border: 1px dashed var(--neutral-300, #d4d4d4);
    left: 0 !important;
}

.upload-img-v2 .settings-upload-v4 .img-wrp {
    background: transparent !important;
    min-width: auto !important;
}

.upload-img-v2 .settings-upload-v4 .img-wrp img {
    height: 70px;
}

@media (max-width: 575px) {
    .mybazar-login-section .button-group {
        -ms-grid-columns: 50% auto;
        grid-template-columns: 50% auto;
    }

    .mybazar-login-section .maan-main-content {
        padding: 25px 10px;
    }

    .total-count-area {
        gap: 10px !important;
    }

    .total-count-area .count-item {
        padding: 15px !important;
    }
    .total-count-area .count-item h5 {
        font-size: 20px;
        line-height: 1;
    }
}

.mybazar-login-section .hide-pass img:last-child {
    display: none;
}

.mybazar-login-section .show-pass img:first-child {
    display: none;
}

.mybazar-login-section .show-pass img:last-child {
    display: block;
}

.mybazar-login-section .hide-pass {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

/*--------------------------------------
    - login css end
----------------------------------------*/
.login-button-list {
    margin-top: 20px;
}

.login-button-list .theme-btn {
    min-width: 140px;
    border-radius: 12px;
    color: #fff !important;
}

.login-button-list li {
    display: inline-block;
}

.login-button-list li:nth-child(1) a {
    background: #15d9a9;
}

.login-button-list li:nth-child(2) a {
    background: #3751fe;
}

.login-button-list li:nth-child(3) a {
    background: #ff8400;
}

.login-button-list li:nth-child(4) a {
    background: #ff55b1;
}

.login-button-list li:nth-child(5) a {
    background: #0b4370;
}

.login-button-list li:nth-child(6) a {
    background: #a44e43;
}

.login-button-list li:nth-child(7) a {
    background: #1d89e3;
}

.login-button-list li:nth-child(8) a {
    background: #344857;
}

.login-button-list ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    gap: 10px;
}

.delete-massage {
    text-align: center;
}

.aler-icons {
    height: 50px;
    width: 50px;
    line-height: 50px;
    color: red;
    border: 1px solid red;
    border-radius: 50%;
    margin: 0 auto;
    margin-bottom: 15px;
}

.erp-dashboard-profile img {
    width: 100%;
}

.erp-dashboard-profile .profile-bg {
    height: 150px;
}

.erp-dashboard-profile .profile-bg img {
    height: 150px;
    -o-object-fit: cover;
    object-fit: cover;
}

.erp-dashboard-profile .profile-img {
    height: 80px;
    width: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    margin-top: -40px;
    border: 2px solid #292be9;
}

.erp-dashboard-profile-section {
    padding: 20px;
}

.order-summary-tab {
    margin-top: -34px;
}

/* print css start css */
.print-wrapper .table th,
.print-wrapper .table td {
    white-space: pre-wrap;
    vertical-align: middle;
}

.print-wrapper .daily-production-table-print th,
.print-wrapper .daily-production-table-print td {
    padding: 5px !important;
}

.print-wrapper .container {
    margin: 0 auto;
}

.print-wrapper .print-btn {
    position: absolute;
    right: 30px;
    top: 30px;
    float: none;
}

.print-wrapper .ml-auto {
    margin-left: auto;
}

.print-signature-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    /* margin-top: 100px; */
}

.print-signature-wrapper p {
    border-top: 1px solid #000;
    width: 200px;
    text-align: center;
    padding-top: 15px;
}

.bg-gray {
    background: rgba(31, 31, 31, 0.05) !important;
    color: #000 !important;
}

/* print css end css*/
.table-footer-last {
    max-width: 350px;
    margin-left: auto;
    margin-top: 100px;
}

.table-footer-last td {
    text-align: left;
    font-weight: 500;
    color: #000;
}

.table-footer-last td:last-child {
    text-align: right;
    font-weight: 700;
    color: #000;
}

.modal-btn-ctg {
    height: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    color: #fff !important;
    background: #292be9;
}

.input-group .form-control {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
}

.daily-transaction-between-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 15px;
    padding: 16px;
}

.daily-transaction-between-wrapper .between-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: 1px solid #ced4da;
    overflow: hidden;
    border-radius: 6px;
}

.daily-transaction-between-wrapper .between-wrapper input {
    border: none;
    padding: 0;
    margin: 0 10px;
    min-width: 90px !important;
    text-align: center;
    outline: none;
}

.daily-transaction-between-wrapper
    .between-wrapper
    input[type="date"]::-webkit-inner-spin-button,
.daily-transaction-between-wrapper
    .between-wrapper
    input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
}

.daily-transaction-between-wrapper .between-wrapper span {
    background: #6c6d78;
    display: inline-block;
    padding: 6px 10px;
    color: #fff;
}

.daily-transaction-between-wrapper .between-wrapper .nice-select {
    height: 37px;
    border-radius: 6px;
}

.this-month-select {
    padding-left: 0;
}

.this-month-select .current {
    font-weight: 500;
}

.total-count-area {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
}

.total-count-area .count-item {
    border-radius: 10px;
    max-width: 230px;
    padding: 15px 20px;
    width: 100%;
}

.total-count-area .count-item h5 {
    font-weight: 600;
    font-size: 23px;
    line-height: 31px;
    color: #2e2e3e;
    margin-bottom: 5px;
}

.total-count-area .count-item p {
    font-size: 15px;
    font-weight: 500;
}

.light-blue {
    background: rgba(45, 176, 246, 0.2);
}

.light-green {
    background: rgba(21, 205, 117, 0.2);
}

.light-orange {
    background: rgba(255, 140, 0, 0.2);
}

.light-red {
    background: rgba(255, 37, 37, 0.2);
}

.input-group
    > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
        .valid-feedback
    ):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: 0;
}

.add-suplier-modal-wrapper .input-group {
    border: 1px solid #ced4da;
    border-radius: 6px;
    margin-left: 0 !important;
}

.add-suplier-modal-wrapper .input-group .nice-select {
    border: none;
    background: none;
}

.add-suplier-modal-wrapper .input-group .input-group-text {
    border: none;
}

.service-btn-possition {
    background: #292be9 !important;
    color: #fff !important;
    font-size: 20px;
    height: 40px;
    width: 40px;
    border: none;
    outline: none;
}

.grid-5 {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 15px 1fr 15px 1fr 15px 1fr 15px 1fr;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
}

.grid-10 {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 15px 1fr 15px 1fr 15px 1fr 15px 1fr 15px 1fr 15px 1fr
        15px 1fr 15px 1fr 15px 1fr;
    grid-template-columns: repeat(10, 1fr);
    gap: 15px;
}

.grid-4 {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 15px 1fr 15px 1fr 15px 1fr;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.grid-3 {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 15px 1fr 15px 1fr;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.feature-row {
    position: relative;
    padding-right: 60px;
}

.feature-row .remove-btn-features {
    position: absolute;
    top: 10px;
    right: 0;
}

.loss-profit .nice-select,
.loss-profit select {
    min-width: 180px;
}

.loss-profit .current {
    line-height: 1.5;
}

.form-check-input:focus {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.form-switch {
    margin: 0;
    padding: 0;
}

.form-switch .form-check-input {
    margin: 0 auto;
    padding: 0;
    height: 20px;
    width: 40px !important;
    float: none;
    border: 1px solid #ddd !important;
}

.nice-select,
select {
    min-width: 120px;
}

.print-btn {
    width: auto;
    min-width: 30px;
    padding: 5px 15px;
}

.print-btn i {
    margin-right: 6px;
}

.ledger-detais-date {
    max-width: 300px;
    margin: 0 auto;
    margin-top: 20px;
}

.ledger-detais-date .input-wrapper-between input {
    min-width: unset !important;
    width: 90px;
}

.ledger-detais-date .input-wrapper-between small {
    font-weight: 500;
    font-size: 15px;
}

.form-switch .form-check-input:checked {
    background-color: #292be9;
}

.commercial-invoice tr,
.commercial-invoice td,
.commercial-invoice th {
    font-size: 16px;
    text-align: left;
    white-space: normal !important;
    height: 40px;
}

.party-ledger tr,
.party-ledger td,
.party-ledger th {
    font-size: 16px;
    text-align: left;
    white-space: nowrap !important;
    height: 40px;
}

.commercial-invoice tr p,
.commercial-invoice td p,
.commercial-invoice th p {
    font-size: 16px;
    line-height: 30px;
}

.commercial-invoice.text-center td,
.commercial-invoice.text-center th,
.commercial-invoice.text-center tr {
    text-align: center;
}

.sample-form-wrp {
    padding-right: 180px;
}

.sample-form-wrp .service-btn-possition {
    position: absolute;
    right: 60px;
    top: 40px;
}

.sample-form-wrp .remove-btn-features {
    top: 40px;
    right: 15px;
}

table input {
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    outline: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    /* width: 100% !important; */
    font-size: 12px !important;
}

.mw-1000 {
    min-width: 1000px;
}

.table-form-section {
    position: relative;
}

.table-form-section .responsive-table {
    padding-left: 20px;
}

.table-form-section .add-btn-one,
.table-form-section .add-btn-two,
.table-form-section .add-btn-three,
.table-form-section .add-btn-four,
.table-form-section .add-btn-five,
.table-form-section .add-btn-six,
.table-form-section .add-btn-seven {
    position: absolute;
    left: -20px;
    top: 0px;
    height: 20px;
    width: 20px;
    background: rgba(255, 132, 0, 0.5);
    border-radius: 3px;
    color: #000;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
}

td.position-relative,
tr.position-relative {
    padding: 0;
}

td.position-relative td,
tr.position-relative td {
    padding: 0;
}

.w-120 {
    min-width: 120px;
}

.form-table-sm tr td:first-child {
    width: 140px;
}

.tr-remove-btn {
    position: absolute;
    left: -20px;
    top: 30px;
    color: red;
    cursor: pointer;
    height: 20px;
    width: 20px;
    text-align: center;
    line-height: 20px;
    display: none;
}

.dataTables_wrapper .dt-buttons {
    float: right;
    margin-bottom: 30px;
}

.dataTables_wrapper .dt-buttons button {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    background: none;
    font-size: 18px;
    color: rgba(12, 25, 40, 0.5);
}

.dataTables_wrapper .dataTables_filter {
    float: left;
    margin-bottom: 30px;
    width: 200px;
    margin-left: 15px;
}

.dataTables_wrapper .dataTables_filter label {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 10px;
    color: #fff;
    height: 40px;
}

.dataTables_wrapper .dataTables_filter label input {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 200px;
}

.dataTables_wrapper .dataTables_length {
    float: left;
    margin-bottom: 30px;
    width: 150px;
}

.dataTables_wrapper .dataTables_length label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 10px;
    color: #fff;
    position: relative;
    min-height: 40px;
}

.dataTables_wrapper .dataTables_length label select {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 1;
}

.dataTables_wrapper select,
.dataTables_wrapper input {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    -webkit-transition: border-color 0.15s ease-in-out,
        -webkit-box-shadow 0.15s ease-in-out;
    transition: border-color 0.15s ease-in-out,
        -webkit-box-shadow 0.15s ease-in-out;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
        -webkit-box-shadow 0.15s ease-in-out;
    outline: none;
    padding-right: 15px;
}

.dataTables_info {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.7;
    float: left;
}

.dataTables_paginate {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    float: right;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 3px 10px;
    display: inline-block;
    border: none !important;
    border: 1px solid #eee !important;
    line-height: 20px;
    margin: 0;
    color: #f88808 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #f88808;
    color: #fff !important;
}

.nice-select .list {
    width: 100%;
}

.A4-paper {
    max-width: 1100px;
    margin: 0 auto;
}

.print-img {
    margin-left: 50px;
}

.w-400 {
    max-width: 400px;
}

/* invoice css start */
.invoice-section {
    max-width: 800px;
    margin: 0 auto;
}

.invoice-container {
    padding: 0 20px;
}

.invoice-header {
    background: #dbeef4;
    padding: 5px 0;
}

.invoice-header h1 {
    font-family: "Manrope";
    font-weight: 600;
    font-size: 24px;
    line-height: 68px;
    color: #191919;
    text-align: right;
}

.invoice-header h2 {
    font-family: "Manrope";
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
    color: #191919;
}

.invoice-header p {
    font-family: "Manrope";
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #555e69;
}

.invoice-address-section {
    padding: 10px 0;
}

.invoice-address-section li {
    font-family: "Manrope";
    font-weight: 400;
    font-size: 13px;
    line-height: 23px;
    color: #0c1928;
    display: flex;
    align-items: center;
    gap: 5px;
}

.invoice-address-section li p,
.invoice-address-section li span {
    font-family: "Manrope";
    font-weight: 400;
    font-size: 13px;
    line-height: 23px;
    color: #0c1928;
}

.invoice-table thead th {
    border: 1px solid #7e7e7e;
    background: #dbeef4;
    font-style: normal;
    font-weight: 500;
    font-size: 10px;
    line-height: 24px;
    color: #191919;
    padding: 0px 5px;
}

.invoice-table tbody td {
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 20px;
    color: #475467;
    border: 1px solid #7e7e7e;
    text-align: left;
    white-space: normal;
    padding: 0px 5px;
}

.invoice-table tbody td p,
.invoice-table tbody td span {
    font-size: 10px !important;
}

.invoice-table tbody td:last-child {
    text-align: right;
}

.invoice-table tfoot th {
    border: 1px solid #7e7e7e;
    background: #dbeef4;
    font-style: normal;
    font-weight: 600;
    font-size: 10px;
    line-height: 24px;
    color: #191919;
    padding: 0px 5px;
}

.invoice-table tfoot td {
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: #475467;
    border: 1px solid #7e7e7e;
    text-align: left;
    white-space: normal;
}

.invoice-table tfoot td:last-child {
    text-align: right;
}

.invoice-table tfoot td span,
.invoice-table tfoot td p {
    max-width: 100% !important;
    font-size: 10px;
}

.invoice-bank-details {
    margin-top: 10px;
}

.invoice-bank-details h4 {
    display: inline-block;
    font-family: "Manrope";
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 33px;
    color: #0c1928;
    border-bottom: 1px solid #0c1928;
    margin-bottom: 10px;
}

.invoice-bank-details li {
    margin: 0;
}

.invoice-bank-details li span,
.invoice-bank-details li p {
    max-width: 100%;
    color: #0c1928;
}

.invoice-others-section {
    padding: 20px 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.invoice-others-section h6 {
    font-family: "Manrope";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    max-width: 220px;
    line-height: 1.4;
    color: #0c1928;
}

.invoice-signature-section {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 30px 0;
}

.invoice-signature-section p {
    display: inline-block;
    color: #0c1928;
    border-top: 1px solid #0c1928;
}

.invoice-footer {
    background: #dbeef4;
    padding: 10px 0;
    text-align: center;
    margin-top: 30px;
}

.invoice-footer a {
    font-size: 10px;
    color: #0c1928 !important;
}

/* ---- blade-up-down-arrow css start --- */
.blade-up-down-arrow select {
    cursor: pointer;
}

.blade-up-down-arrow span {
    border-bottom: 2px solid #999;
    border-right: 2px solid #999;
    pointer-events: none;
    content: "";
    position: absolute;
    top: 40%;
    right: 20px;
    height: 10px;
    width: 10px;
    -webkit-transform-origin: 66% 66%;
    -ms-transform-origin: 66% 66%;
    transform-origin: 66% 66%;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transition: all 0.15s ease-in-out;
    transition: all 0.15s ease-in-out;
}

.blade-up-down-arrow:hover span {
    transform: rotate(225deg);
}

.remove-bg-select {
    background: transparent !important;
    border: 1px solid transparent !important;
}

.remove-bg-table-select {
    background: transparent !important;
}

.table-select-lebel {
    z-index: 4 !important;
}

@media print {
    .invoice-address-section li {
        margin: 0;
    }

    .invoice-address-section p,
    .invoice-address-section span {
        font-size: 10px !important;
        line-height: 20px;
        max-width: 200px !important;
    }

    .invoice-address-section span {
        min-width: 110px !important;
    }

    .invoice-table thead th {
        font-size: 10px;
    }

    .invoice-table tbody td {
        font-size: 10px;
        text-align: center;
    }

    .invoice-header h1 {
        text-align: right;
    }
}

/* invoice css end */
@media (max-width: 1700px) {
    .state-overview-box .icons {
        height: 70px;
        width: 70px;
        min-width: 70px;
        line-height: 70px;
    }

    .state-overview-box .icons img {
        height: 40px;
    }

    .state-overview-box {
        padding: 8px;
    }

    .state-overview-box h2 {
        font-size: 20px;
    }

    .state-overview-box p {
        font-size: 14px;
    }
}

@media (max-width: 1400px) {
    .erp-state-overview-wrapper {
        -ms-grid-columns: (1fr) [3];
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1150px) {
    .table-top-form {
        display: block;
    }

    .table-top-btn-group ul {
        margin-top: 20px;
    }

    .grid-5,
    .grid-3,
    .grid-4 {
        -ms-grid-columns: 1fr 15px 1fr;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .table-top-form .form-control {
        margin: 0;
    }

    .side-bar {
        left: -100%;
    }

    .section-container {
        padding-left: 0 !important;
    }

    .header-middle {
        display: none;
    }

    .header-wrapper {
        -ms-grid-columns: 50% 50%;
        grid-template-columns: 50% 50%;
    }

    .language-change {
        display: none;
    }

    .w-50 {
        width: 100% !important;
    }

    .side-bar {
        width: 300px !important;
    }

    .side-bar .side-bar-manu .dropdown::before {
        opacity: 1 !important;
    }

    .side-bar .sidebar-icon img {
        width: 16px !important;
    }

    .side-bar .side-bar-manu li a .sidebar-icon {
        left: 0 !important;
        margin-right: 10px !important;
    }

    .side-bar.active {
        left: 0;
    }

    .close-btn {
        display: block;
        font-size: 20px;
        cursor: pointer;
    }

    .grid-5 {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        gap: 15px;
    }
}

@media (min-width: 1150px) {
    .table-top-form .order-search {
        width: 140px !important;
    }
}

@media (max-width: 768px) {
    .order-summary-tab {
        margin-top: 25px;
    }

    .grid-5 {
        -ms-grid-columns: (1fr) [1];
        grid-template-columns: repeat(1, 1fr);
    }

    .table-header {
        display: block;
    }

    .table-header .button-group {
        margin-top: 20px;
    }

    .container-fluid {
        padding: 0 15px;
    }

    .add-suplier-modal-wrapper {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }

    .add-suplier-modal-wrapper .row {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2;
    }

    .add-suplier-modal-wrapper .add-profile-photo {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1;
        margin: 0 auto !important;
    }

    .erp-state-overview-wrapper {
        -ms-grid-columns: (1fr) [2];
        grid-template-columns: repeat(2, 1fr);
    }

    .daily-transaction-between-wrapper {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }

    .daily-transaction-between-wrapper .between-wrapper input {
        font-size: 12px;
    }
}

@media (max-width: 575px) {
    .erp-state-overview-wrapper {
        -ms-grid-columns: (1fr) [1];
        grid-template-columns: repeat(1, 1fr);
    }

    .voucher-big-box-wrapper {
        gap: 20px;
    }
}

.print-inner-page {
    display: none !important;
}

@media print {
    .table-header {
        display: none;
    }

    .side-bar,
    .main-header-section,
    .print-btn {
        display: none;
    }

    .section-container {
        padding: 0;
        /* width: 700px; */
    }

    .responsive-table {
        overflow: visible !important;
        text-align: center !important;
    }

    .print-inner-page {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important;
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }

    .daily-transaction-between-wrapper,
    .total-count-area,
    .print-d-none {
        display: none;
    }

    .A4-paper {
        max-width: 100% !important;
        margin: 0 auto !important;
    }
}

/* new invoice css start */
.erp-new-invice {
    max-width: 950px;
    margin: 0 auto;
    position: relative;
    min-height: 1000px;
}

.erp-new-invice .table-header h3 strong {
    color: #000;
    font-size: 25px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    font-family: "Lato", sans-serif;
}

.erp-new-invice .table-header p {
    color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    font-family: "Lato", sans-serif;
}

.erp-new-invice .bill-invoice-wrp {
    text-align: center;
    margin: 15px 0;
    position: relative;
}

.erp-new-invice .bill-invoice-wrp .paking-date {
    position: absolute;
    right: 0;
    top: 0;
    color: #000;
    font-family: Lato;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: uppercase;
}

.erp-new-invice .bill-invoice-wrp h2 {
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    font-family: "Lato", sans-serif;
}

.erp-new-invice .bill-invoice-wrp hr {
    margin: 0;
    max-width: 170px;
    margin: 0 auto;
    height: 2px;
    border-top: 2px solid #000;
    opacity: 1;
}

.erp-new-invice .invice-detaisl {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-family: "Lato", sans-serif;
    gap: 50px;
}

.erp-new-invice .invice-detaisl .bill-left-side,
.erp-new-invice .invice-detaisl .bill-right-side {
    width: 50%;
    margin-bottom: 15px;
}

.erp-new-invice .invice-detaisl .bill-left-side p,
.erp-new-invice .invice-detaisl .bill-right-side p {
    line-height: 19px;
    color: #000;
}

.erp-new-invice .invice-detaisl .address-items {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 5px;
}

.erp-new-invice .invice-detaisl .address-items strong {
    display: block;
    width: 130px;
    color: #000;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    font-family: "Lato", sans-serif;
}

.erp-new-invice .invice-detaisl .address-items p {
    color: #000;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    font-family: "Lato", sans-serif;
}

.erp-new-invice .bill-right-side {
    margin-left: auto;
    text-align: right;
}

.erp-new-invice th {
    background: transparent;
    border: 1px solid #000;
    color: #000;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    font-family: "Lato", sans-serif;
}

.erp-new-invice tr {
    border: none;
}

.erp-new-invice td {
    color: #000;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    font-family: "Lato", sans-serif;
    border: none;
    border-right: 1px solid #000;
    border-left: 1px solid #000;
}

.erp-new-invice td p {
    color: #000;
    font-family: Lato;
    font-size: 13px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    font-family: "Lato", sans-serif;
}

.erp-new-invice td small {
    color: #000;
    font-family: "Lato", sans-serif;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.erp-new-invice table .address-items {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 10px;
}

.erp-new-invice table .address-items p,
.erp-new-invice table .address-items strong {
    font-size: 13px;
    color: #000;
    line-height: 1;
}

.erp-new-invice table hr {
    opacity: 1;
    margin: 0;
    margin-bottom: 10px;
}

.erp-new-invice .signature {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100% !important;
    max-width: 100% !important;
    border: none;
    margin-top: 100px;
    /* position: absolute; */
    /* top: auto; */
    /* bottom: 0;

    position: fixed; */
    /* position: absolute;
    top: auto;
    bottom: 0; */
}

.erp-new-invice .signature p {
    display: inline-block;
    border-top: 1px solid #000;
    color: #000;
    font-size: 13px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding-top: 10px;
    font-family: "Lato", sans-serif;
}

.erp-new-invice .table-header img {
    max-width: 300px;
}

.erp-new-invice .invoice-text {
    background: #f4efe5;
    text-align: right;
    padding-right: 100px;
    margin-bottom: 30px;
}

.erp-new-invice .invoice-text h2 {
    color: #000;
    font-family: Lato;
    font-size: 35px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    text-transform: uppercase;
    display: inline-block;
    padding: 0 15px;
    background: #fff;
}

.invoice-two thead th {
    background: rgba(172, 133, 55, 0.13) !important;
}

.invoice-two td {
    border: 1px solid #000;
}

.invoice-two th h2 {
    color: #1f1f1f;
    font-family: Lato;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.qty-new {
    min-width: 120px;
}

.invoice-payment-details h3 {
    display: inline-block;
    border-bottom: 1px solid #000;
    color: #1f1f1f;
    font-family: Lato;
    font-size: 26px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.invoice-payment-details p {
    color: #000;
    font-family: Lato;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.new-invoice-others {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 40px;
}

.new-invoice-others h5 {
    max-width: 340px;
    color: #000;
    font-family: Lato;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.new-invoice-footer {
    text-align: center;
    margin-top: 40px;
}

.new-invoice-footer p {
    background: rgba(172, 133, 55, 0.13);
    padding: 15px 0;
    color: #000;
    font-family: Lato;
    font-size: 19px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.production-report-header {
    border: 1px solid #000;
    background: #ffe6cd;
    padding: 10px;
    color: #000;
    font-family: Lato;
    font-size: 25px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.erp-table-section .table-container {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translate(-50%, -5%);
}

.paking-detail-table td {
    border: 1px solid #000;
}

.closing-balance {
    color: #000;
    text-align: right;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    gap: 80px;
}

/* new invoice css end */

/* new css start 14/08/2023 */
.order-form-section .form-control {
    min-width: 70px;
    min-width: 70px;
}

.table td {
    vertical-align: center;
}

/* new css end 14/08/2023 */
.notifications ul {
    overflow: auto;
    max-height: 620px;
}

/* new dashboard css start */
.erp-overview-grid-6 {
    display: -ms-grid;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
    -ms-grid-columns: 1fr 25px 1fr 25px 1fr 25px 1fr 25px 1fr 25px 1fr;
    grid-template-columns: repeat(6, 1fr);
    gap: 25px;
}

.erp-overview-item {
    border-radius: 12px;
    background: #fff;
    -webkit-box-shadow: 0px 4px 24px 0px rgba(195, 208, 228, 0.3);
    box-shadow: 0px 4px 24px 0px rgba(195, 208, 228, 0.3);
    padding: 15px;
    -webkit-box-pack: stretch;
    -ms-flex-pack: stretch;
    justify-content: stretch;
    display: flex;
    align-items: center;
}

.erp-overview-item .overview-icon {
    height: 45px;
    width: 45px;
    padding: 10px;
    border-radius: 10px;
    background: #edd9ff;
}

.erp-overview-item .overview-icon img {
    width: 100%;
}

.erp-overview-item p {
    color: #344054;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}

.erp-overview-item h6 {
    color: #101828;
    font-size: 20px;
    line-height: 30px;
    font-style: normal;
    font-weight: 600;
}

.erp-overview-item .overview-icon {
    margin-right: 5px;
}

.erp-overview-item:nth-child(2) .overview-icon {
    background: #e1dfff;
}

.erp-overview-item:nth-child(3) .overview-icon {
    background: #ffe9de;
}

.erp-overview-item:nth-child(4) .overview-icon {
    background: #cbffeb;
}

.erp-overview-item:nth-child(5) .overview-icon {
    background: #ddecff;
}

.erp-overview-item:nth-child(6) .overview-icon {
    background: #fde7fc;
}

.erp-overview-item:nth-child(7) .overview-icon {
    background: #ddecff;
}

.erp-overview-item:nth-child(8) .overview-icon {
    background: #cbffeb;
}

.erp-overview-item:nth-child(9) .overview-icon {
    background: #edd9ff;
}

.erp-overview-item:nth-child(10) .overview-icon {
    background: #edd9ff;
}

.erp-overview-item:nth-child(11) .overview-icon {
    background: #ddecff;
}

.erp-overview-item:nth-child(12) .overview-icon {
    background: #e1dfff;
}

.dashboard-card {
    background: #ffffff;
    -webkit-box-shadow: 0px 0px 1px rgba(65, 76, 105, 0.24),
        0px 3px 8px -1px rgba(50, 57, 71, 0.05);
    box-shadow: 0px 0px 1px rgba(65, 76, 105, 0.24),
        0px 3px 8px -1px rgba(50, 57, 71, 0.05);
    border-radius: 12px;
    height: 360px !important;
}

.dashboard-card .dashboard-card-body {
    padding: 0px 20px;
}

.dashboard-card .table-responsive {
    border-radius: 8px;
    -webkit-box-shadow: 0px 0px 1px rgba(65, 76, 105, 0.24),
        0px 3px 8px -1px rgba(50, 57, 71, 0.05);
    box-shadow: 0px 0px 1px rgba(65, 76, 105, 0.24),
        0px 3px 8px -1px rgba(50, 57, 71, 0.05);
}

.dashboard-card .dashboard-card-header {
    padding: 10px 20px !important;
}

.dashboard-card .income-header {
    background-color: #ffe3ed;
}

.dashboard-card .new-order-header {
    background-color: #f3ffc5;
}

.dashboard-card .sales-ratio-header {
    background-color: #af49ff26;
}

.dashboard-card .sales-country-header {
    background-color: #1aff2326;
}

.dashboard-card .buyer-header {
    background-color: #ffa31a26;
}

.dashboard-card .dashboard-card-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 15px;
}

.dashboard-card .dashboard-card-header p,
.dashboard-card .dashboard-card-footer p {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #667085;
}

.dashboard-card-footer {
    text-align: right;
    padding-top: 15px;
}

.dashboard-card .dashboard-card-header a,
.dashboard-card .dashboard-card-footer a {
    color: #1570ef !important;
    font-weight: 400;
}

.dashboard-card .dashboard-card-header h4 {
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 28px;
    color: #101828;
}

.dashboard-card .dashboard-card-header .nice-select {
    background: transparent;
    border: 1px solid #98a2b3;
    -webkit-box-shadow: 0px 0px 1px rgba(65, 76, 105, 0.24),
        0px 3px 8px -1px rgba(50, 57, 71, 0.05);
    box-shadow: 0px 0px 1px rgba(65, 76, 105, 0.24),
        0px 3px 8px -1px rgba(50, 57, 71, 0.05);
    border-radius: 6px;
    -webkit-box-shadow: none;
    box-shadow: none;
    height: 36px;
    line-height: 24px;
}

.dashboard-card .dashboard-card-header .nice-select::after {
    height: 10px;
    width: 10px;
    border-width: 1px;
    border-color: #667085;
    top: 40%;
}


/* custom table css start */
.customth-first{
    text-align: left !important;
    padding-left: 35px !important;
}
.customth-last{
    text-align: right !important;
    padding-right: 35px !important;
}

.customtd-first{
    text-align: left !important;
    padding-left: 40px !important;
}
 .customtd-last{
    display: flex;
    justify-content: end;
    padding-right: 40px !important;
}


@media only screen and (min-width: 1150px) and (max-width: 1400px) {
    .flex-wrap-xl-xxl {
        flex-wrap: wrap;
    }
    .w-150-xl-xxl {
        width: 150px;
    }
}
@media only screen and (min-width: 1500px) and (max-width: 1650px) {
    .erp-overview-item {
        padding: 10px;
    }

    .erp-overview-item p {
        font-size: 12px !important;
        line-height: 20px !important;
    }

    .erp-overview-item h6 {
        font-size: 15px !important;
        line-height: 25px !important;
    }

    .erp-overview-item .overview-icon {
        height: 40px;
        width: 40px;
        padding: 8px;
    }
}

@media only screen and (min-width: 1199px) and (max-width: 1400px) {
    .dashboard-card .dashboard-card-header h4 {
        font-size: 14px;
        line-height: 16px;
    }
}

@media only screen and (min-width: 1350px) and (max-width: 1500px) {
    .erp-overview-grid-6 {
        grid-template-columns: repeat(4, 1fr);
    }

    .erp-overview-item {
        padding: 10px;
    }

    .erp-overview-item p {
        font-size: 12px !important;
        line-height: 20px !important;
    }
}

@media (max-width: 1366px) {
    .header-wrapper {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }

    .erp-overview-item h6 {
        font-size: 20px;
        line-height: 28px;
    }

    .erp-overview-grid-6 {
        grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
    }
}

@media (max-width: 1150px) {
    .due-list-filter {
        display: flex;
        flex-wrap: wrap;
    }

    .due-list-filter .dataTables_length {
        margin-bottom: 20px;
    }

    .due-list-filter .nice-select {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .total-count-area {
        display: -ms-grid;
        display: grid;
        -ms-grid-columns: (1fr) [2];
        grid-template-columns: repeat(2, 1fr);
    }
    .total-count-area .count-item {
        max-width: 100%;
    }
}

@media (max-width: 767px) {
    .due-list-filter .nice-select {
        width: 100%;
    }

    .erp-overview-grid-6 {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        gap: 15px;
    }

    .erp-overview-item .overview-icon {
        height: 40px;
        width: 40px;
    }

    .erp-overview-item p {
        font-size: 10px;
        line-height: 18px;
    }

    .erp-overview-item h6 {
        font-size: 15px;
        line-height: 22px;
    }

    .dashboard-card {
        padding: 15px;
    }

    .dashboard-card .dashboard-card-header h4 {
        font-size: 13px;
    }
}

@media (max-width: 400px) {
    .total-count-area {
        display: grid;
        grid-template-columns: repeat(1, 1fr) !important;
    }
}

/* new dashboard css end */
.print-wrapper {
    min-height: 1000px;
    position: relative;
}

.print-signature-wrapper {
    position: absolute;
    top: auto;
    bottom: 0;
    width: 100%;
    left: 0;
}

.erp-new-invice .table-header p {
    display: none;
}

@media print {
    .table-two thead td {
        font-size: 8px !important;
        padding: 5px !important;
        line-height: 14px;
    }

    .table-two th {
        font-size: 10px !important;
        padding: 5px !important;
        line-height: 14px;
    }
}

/* end */
