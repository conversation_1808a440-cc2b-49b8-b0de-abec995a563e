<div class="table-responsive">
    <table class="table table-striped" id="buyers-table">
        <thead>
            <tr>
                <th>{{ __('S/N') }}</th>
                <th>{{ __('Buyer Name') }}</th>
                <th>{{ __('Total Variations') }}</th>
                <th>{{ __('Total Shipped') }}</th>
                <th>{{ __('Total Used') }}</th>
                <th>{{ __('Available Stock') }}</th>
                <th>{{ __('Low Stock Items') }}</th>
                <th>{{ __('Out of Stock Items') }}</th>
                <th>{{ __('Actions') }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse($buyersStock as $index => $buyerStock)
            @php
                $buyer = $buyerStock['buyer'];
                $summary = $buyerStock['summary'];
            @endphp
            <tr class="{{ $summary['out_of_stock_items'] > 0 ? 'table-danger' : ($summary['low_stock_items'] > 0 ? 'table-warning' : '') }}">
                <td>{{ $index + 1 }}</td>
                <td>
                    <div>
                        <strong>{{ $buyer->name }}</strong>
                        <br>
                        <small class="text-muted">{{ $buyer->user->email ?? '-' }}</small>
                    </div>
                </td>
                <td>{{ $summary['total_variations'] }}</td>
                <td>{{ $summary['total_shipped'] }}</td>
                <td>{{ $summary['total_used'] }}</td>
                <td>
                    <strong class="{{ $summary['total_available'] <= 0 ? 'text-danger' : ($summary['total_available'] <= 50 ? 'text-warning' : 'text-success') }}">
                        {{ $summary['total_available'] }}
                    </strong>
                </td>
                <td>
                    @if($summary['low_stock_items'] > 0)
                        <span class="badge badge-warning">{{ $summary['low_stock_items'] }}</span>
                    @else
                        <span class="text-muted">0</span>
                    @endif
                </td>
                <td>
                    @if($summary['out_of_stock_items'] > 0)
                        <span class="badge badge-danger">{{ $summary['out_of_stock_items'] }}</span>
                    @else
                        <span class="text-muted">0</span>
                    @endif
                </td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown" aria-expanded="false">
                            {{ __('Actions') }}
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.buyer-stock.show', $buyer->id) }}">
                                    <i class="fas fa-eye"></i> {{ __('View Details') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.buyer-stock.usage-history', $buyer->id) }}">
                                    <i class="fas fa-history"></i> {{ __('Usage History') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.buyer-stock.export', $buyer->id) }}">
                                    <i class="fas fa-download"></i> {{ __('Export CSV') }}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('parties.show', $buyer->id) }}">
                                    <i class="fas fa-user"></i> {{ __('View Buyer Profile') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="9" class="text-center">{{ __('No buyers found') }}</td>
            </tr>
            @endforelse
        </tbody>
    </table>
</div>

@if($buyersStock->count() > 0)
<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="count-item light-blue">
            <h5><span class="counter">{{ $buyersStock->count() }}</span></h5>
            <p>{{ __('Total Buyers') }}</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="count-item light-green">
            <h5><span class="counter">{{ $buyersStock->sum(function($item) { return $item['summary']['total_shipped']; }) }}</span></h5>
            <p>{{ __('Total Items Shipped') }}</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="count-item light-orange">
            <h5><span class="counter">{{ $buyersStock->sum(function($item) { return $item['summary']['total_used']; }) }}</span></h5>
            <p>{{ __('Total Items Used') }}</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="count-item light-purple">
            <h5><span class="counter">{{ $buyersStock->sum(function($item) { return $item['summary']['total_available']; }) }}</span></h5>
            <p>{{ __('Total Available Stock') }}</p>
        </div>
    </div>
</div>

<!-- Alert Summary -->
@php
    $totalLowStock = $buyersStock->sum(function($item) { return $item['summary']['low_stock_items']; });
    $totalOutOfStock = $buyersStock->sum(function($item) { return $item['summary']['out_of_stock_items']; });
    $buyersWithIssues = $buyersStock->filter(function($item) { 
        return $item['summary']['low_stock_items'] > 0 || $item['summary']['out_of_stock_items'] > 0; 
    })->count();
@endphp

@if($totalLowStock > 0 || $totalOutOfStock > 0)
<div class="row mt-3">
    <div class="col-md-12">
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle"></i> {{ __('Stock Alerts') }}</h6>
            <ul class="mb-0">
                @if($totalOutOfStock > 0)
                    <li><strong>{{ $totalOutOfStock }}</strong> {{ __('items are out of stock across all buyers') }}</li>
                @endif
                @if($totalLowStock > 0)
                    <li><strong>{{ $totalLowStock }}</strong> {{ __('items have low stock (≤10) across all buyers') }}</li>
                @endif
                <li><strong>{{ $buyersWithIssues }}</strong> {{ __('buyers have stock issues that need attention') }}</li>
            </ul>
        </div>
    </div>
</div>
@endif
@endif
