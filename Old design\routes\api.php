<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// API Routes for Garments ERP
Route::middleware('auth:sanctum')->group(function () {
    
    // Orders API
    Route::apiResource('orders', App\Http\Controllers\Api\OrderController::class);
    
    // Production API
    Route::apiResource('productions', App\Http\Controllers\Api\ProductionController::class);
    
    // Accessories API
    Route::apiResource('accessories', App\Http\Controllers\Api\AccessoryController::class);
    
    // Shipments API
    Route::apiResource('shipments', App\Http\Controllers\Api\ShipmentController::class);
    
    // Dashboard Statistics API
    Route::get('dashboard/stats', [App\Http\Controllers\Api\DashboardController::class, 'stats']);
    
    // Reports API
    Route::prefix('reports')->group(function () {
        Route::get('daily-production', [App\Http\Controllers\Api\ReportController::class, 'dailyProduction']);
        Route::get('order-summary', [App\Http\Controllers\Api\ReportController::class, 'orderSummary']);
        Route::get('inventory-status', [App\Http\Controllers\Api\ReportController::class, 'inventoryStatus']);
    });
});
