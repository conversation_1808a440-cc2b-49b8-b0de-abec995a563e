@extends('layouts.master')

@section('main_content')
    <div style="padding: 40px 0; background-color: #f8f9fa; min-height: 100vh;">
        <div class="container-fluid">
            <div style="background-color: #ffffff; border-radius: 16px; padding: 32px; max-width: 1100px; margin: auto; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                <h2 style="margin-bottom: 24px; font-weight: 600; color: #212529;">Edit Item</h2>

                <form action="{{ route('items.update', $item->id) }}" method="POST" style="margin-bottom: 40px;">
                    @csrf
                    @method('PUT')
                    <div style="margin-bottom: 24px;">
                        <label for="date" style="display: block; margin-bottom: 8px; font-weight: 500;">Date:</label>
                        <input type="date" name="date" value="{{ $item->date }}" style="width: 100%; padding: 10px 12px; border: 1px solid #ced4da; border-radius: 8px; font-size: 14px;">
                    </div>
                    <button type="submit" style="padding: 10px 24px; background-color: #0d6efd; color: white; border: none; border-radius: 999px; font-size: 14px;">Update Item</button>
                </form>

                <h3 style="margin-bottom: 24px; font-weight: 600; color: #212529;">Add Item Size</h3>

                <form action="{{ route('item-sizes.store', ['item' => $item->id]) }}" method="POST" style="margin-bottom: 32px;">
                    @csrf
                    <div style="display: flex; flex-wrap: wrap; gap: 20px;">

                        @php
                            $fieldStyle = 'padding: 8px 12px; width: 100%; border: 1px solid #ced4da; border-radius: 8px; font-size: 14px;';
                            $labelStyle = 'margin-bottom: 6px; display: block; font-weight: 500;';
                            $blockStyle = 'flex: 1 1 12%; min-width: 120px;';
                        @endphp

                        <div style="{{ $blockStyle }}">
                            <label style="{{ $labelStyle }}">Part:</label>
                            <select name="part" style="{{ $fieldStyle }}">
                                <option value="back">Back</option>
                                <option value="front">Front</option>
                                <option value="full">Full</option>
                            </select>
                        </div>

                        <div style="{{ $blockStyle }}">
                            <label style="{{ $labelStyle }}">Qty:</label>
                            <input type="number" name="qty" style="{{ $fieldStyle }}">
                        </div>

                        <div style="{{ $blockStyle }}">
                            <label style="{{ $labelStyle }}">Color:</label>
                            <select name="color" style="{{ $fieldStyle }}">
                                <option value="black">Black</option>
                                <option value="blue">Blue</option>
                                <option value="khaki">Khaki</option>
                                <option value="gray">Gray</option>
                                <option value="coffe">Coffee</option>
                            </select>
                        </div>

                        <div style="{{ $blockStyle }}">
                            <label style="{{ $labelStyle }}">Size:</label>
                            <select name="size" style="{{ $fieldStyle }}">
                                @foreach ([28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50] as $s)
                                    <option value="{{ $s }}">{{ $s }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div style="{{ $blockStyle }}">
                            <label style="{{ $labelStyle }}">Style:</label>
                            <select name="style" style="{{ $fieldStyle }}">
                                <option value="Slim Fit">Slim Fit</option>
                                <option value="Loose Fit">Loose Fit</option>
                                <option value="Over Size">Over Size</option>
                                <option value="Formal">Formal</option>
                                <option value="Denim Loose Fit">Denim Loose Fit</option>
                                <option value="Denim Slim Fit">Denim Slim Fit</option>
                            </select>
                        </div>

                        <div style="{{ $blockStyle }}">
                            <label style="{{ $labelStyle }}">Total:</label>
                            <input type="number" name="total" step="0.01" style="{{ $fieldStyle }}">
                        </div>

                        <div style="{{ $blockStyle }}">
                            <label style="{{ $labelStyle }}">Type:</label>
                            <select name="type" style="{{ $fieldStyle }}">
                                <option value="Input">Input</option>
                                <option value="Output">Output</option>
                            </select>
                        </div>

                        <div style="flex: 1 1 100%; margin-top: 10px;">
                            <button type="submit" style="padding: 10px 24px; background-color: #198754; color: white; border: none; border-radius: 999px; font-size: 14px;">
                                + Add Item Size
                            </button>
                        </div>
                    </div>
                </form>

                <h3 style="margin-bottom: 16px; font-weight: 600; color: #212529;">Item Sizes</h3>

                <table style="width: 100%; border-collapse: collapse; background-color: #fff;">
                    <thead style="background-color: #f1f3f5; color: #6c757d;">
                        <tr>
                            @foreach (['Part', 'Qty', 'Color', 'Size', 'Style', 'Total', 'Type', 'Actions'] as $th)
                                <th style="padding: 10px; font-size: 13px; text-transform: uppercase;">{{ $th }}</th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($item->itemSizes as $itemSize)
                            <tr style="border-top: 1px solid #dee2e6;">
                                <td style="padding: 10px;">{{ $itemSize->part }}</td>
                                <td style="padding: 10px;">{{ $itemSize->qty }}</td>
                                <td style="padding: 10px;">{{ $itemSize->color }}</td>
                                <td style="padding: 10px;">{{ $itemSize->size }}</td>
                                <td style="padding: 10px;">{{ $itemSize->style }}</td>
                                <td style="padding: 10px;">{{ $itemSize->total }}</td>
                                <td style="padding: 10px;">{{ $itemSize->type }}</td>
                                <td style="padding: 10px;">
                                    <form action="{{ route('item-sizes.destroy', $itemSize->id) }}" method="POST" onsubmit="return confirm('Delete this size?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" style="padding: 6px 14px; background-color: #dc3545; color: white; font-size: 13px; border: none; border-radius: 999px;">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>

                <div style="margin-top: 30px;">
                    <a href="{{ route('items.index') }}" style="padding: 10px 24px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 999px;">← Back to List</a>
                </div>
            </div>
        </div>
    </div>
@endsection
