@extends('layouts.master', [
    'title' => __('My Stock Dashboard')
])

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4>{{ __('Stock Dashboard') }} - {{ $party->name }}</h4>
                <div class="header-action">
                    <a href="{{ route('buyer.stock.create-usage') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {{ __('Record Usage') }}
                    </a>
                    <a href="{{ route('buyer.stock.usage-history') }}" class="btn btn-info">
                        <i class="fas fa-history"></i> {{ __('Usage History') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Stock Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="count-item light-blue">
                            <h5><span class="counter">{{ $summary['total_variations'] }}</span></h5>
                            <p>{{ __('Total Variations') }}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="count-item light-green">
                            <h5><span class="counter">{{ $summary['total_shipped'] }}</span></h5>
                            <p>{{ __('Total Shipped') }}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="count-item light-orange">
                            <h5><span class="counter">{{ $summary['total_used'] }}</span></h5>
                            <p>{{ __('Total Used') }}</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="count-item light-purple">
                            <h5><span class="counter">{{ $summary['total_available'] }}</span></h5>
                            <p>{{ __('Available Stock') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Alert Cards -->
                @if($summary['out_of_stock_items'] > 0 || $summary['low_stock_items'] > 0)
                <div class="row mb-4">
                    @if($summary['out_of_stock_items'] > 0)
                    <div class="col-md-6">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>{{ $summary['out_of_stock_items'] }}</strong> {{ __('items are out of stock') }}
                        </div>
                    </div>
                    @endif
                    @if($summary['low_stock_items'] > 0)
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-circle"></i>
                            <strong>{{ $summary['low_stock_items'] }}</strong> {{ __('items have low stock (≤10)') }}
                        </div>
                    </div>
                    @endif
                </div>
                @endif

                <!-- Stock Table -->
                <div class="table-responsive">
                    <table class="table table-striped" id="stock-table">
                        <thead>
                            <tr>
                                <th>{{ __('Style') }}</th>
                                <th>{{ __('Color') }}</th>
                                <th>{{ __('Item') }}</th>
                                <th>{{ __('Size') }}</th>
                                <th>{{ __('Total Shipped') }}</th>
                                <th>{{ __('Total Used') }}</th>
                                <th>{{ __('Available Stock') }}</th>
                                <th>{{ __('Status') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($stockData as $item)
                            <tr class="{{ $item['available_stock'] <= 0 ? 'table-danger' : ($item['available_stock'] <= 10 ? 'table-warning' : '') }}">
                                <td>{{ $item['style'] }}</td>
                                <td>{{ $item['color'] ?? '-' }}</td>
                                <td>{{ $item['item'] ?? '-' }}</td>
                                <td>{{ $item['size'] ?? '-' }}</td>
                                <td>{{ $item['total_shipped'] }}</td>
                                <td>{{ $item['total_used'] }}</td>
                                <td>
                                    <strong>{{ $item['available_stock'] }}</strong>
                                </td>
                                <td>
                                    @if($item['available_stock'] <= 0)
                                        <span class="badge badge-danger">{{ __('Out of Stock') }}</span>
                                    @elseif($item['available_stock'] <= 10)
                                        <span class="badge badge-warning">{{ __('Low Stock') }}</span>
                                    @else
                                        <span class="badge badge-success">{{ __('In Stock') }}</span>
                                    @endif
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">{{ __('No stock data available') }}</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Recent Usage -->
                @if($recentUsage->count() > 0)
                <div class="mt-5">
                    <h5>{{ __('Recent Usage') }}</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ __('Date') }}</th>
                                    <th>{{ __('Style') }}</th>
                                    <th>{{ __('Color') }}</th>
                                    <th>{{ __('Item') }}</th>
                                    <th>{{ __('Size') }}</th>
                                    <th>{{ __('Qty Used') }}</th>
                                    <th>{{ __('Purpose') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentUsage as $usage)
                                <tr>
                                    <td>{{ $usage->usage_date->format('M d, Y') }}</td>
                                    <td>{{ $usage->style }}</td>
                                    <td>{{ $usage->color ?? '-' }}</td>
                                    <td>{{ $usage->item ?? '-' }}</td>
                                    <td>{{ $usage->size ?? '-' }}</td>
                                    <td>{{ $usage->qty_used }}</td>
                                    <td>{{ $usage->purpose ?? '-' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    $('#stock-table').DataTable({
        "pageLength": 25,
        "order": [[ 6, "asc" ]], // Sort by available stock ascending
        "columnDefs": [
            {
                "targets": [4, 5, 6], // Numeric columns
                "type": "num"
            }
        ]
    });
});
</script>
@endpush
