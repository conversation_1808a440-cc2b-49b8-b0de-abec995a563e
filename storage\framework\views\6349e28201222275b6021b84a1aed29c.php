

<?php $__env->startSection('title'); ?>
    <?php echo e(__('Production Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('main_content'); ?>
<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-card">
                <div class="welcome-content">
                    <h2 class="welcome-title"><?php echo e(__('Production Dashboard')); ?></h2>
                    <p class="welcome-subtitle"><?php echo e(__('Real-time Production Key Performance Indicators')); ?></p>
                </div>
                <div class="welcome-date">
                    <i class="fas fa-industry me-2"></i>
                    <?php echo e(now()->format('l, F j, Y')); ?>

                </div>
            </div>
        </div>
    </div>

    <!-- Daily Production Targets vs Actual -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-2"><?php echo e(__('Daily Target')); ?></h6>
                            <h2 class="mb-0"><?php echo e(number_format($productionTargets['daily_target'])); ?></h2>
                            <small class="text-white-75"><?php echo e(__('Units Planned')); ?></small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-bullseye fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-2"><?php echo e(__('Daily Actual')); ?></h6>
                            <h2 class="mb-0"><?php echo e(number_format($productionTargets['daily_actual'])); ?></h2>
                            <small class="text-white-75"><?php echo e(__('Units Produced')); ?></small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-industry fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-2"><?php echo e(__('Daily Efficiency')); ?></h6>
                            <h2 class="mb-0"><?php echo e($productionTargets['daily_efficiency']); ?>%</h2>
                            <small class="text-white-75"><?php echo e(__('Target Achievement')); ?></small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-2"><?php echo e(__('Today Cutting')); ?></h6>
                            <h2 class="mb-0"><?php echo e(number_format($productionStats['today_cutting'])); ?></h2>
                            <small class="text-white-75"><?php echo e(__('Units Cut')); ?></small>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-cut fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Line Performance -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?php echo e($linePerformance['line_1_efficiency']); ?>%</h4>
                    <h6 class="text-muted mb-0"><?php echo e(__('Line 1 Efficiency')); ?></h6>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?php echo e($linePerformance['line_2_efficiency']); ?>%</h4>
                    <h6 class="text-muted mb-0"><?php echo e(__('Line 2 Efficiency')); ?></h6>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?php echo e($linePerformance['line_3_efficiency']); ?>%</h4>
                    <h6 class="text-muted mb-0"><?php echo e(__('Line 3 Efficiency')); ?></h6>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?php echo e($linePerformance['average_line_efficiency']); ?>%</h4>
                    <h6 class="text-muted mb-0"><?php echo e(__('Average Efficiency')); ?></h6>
                </div>
            </div>
        </div>
    </div>


    <!-- Work in Progress Status -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-tasks me-2"></i><?php echo e(__('Work in Progress')); ?>

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center p-3 border rounded bg-light">
                                <h4 class="text-warning mb-1"><?php echo e($wipStatus['wip_cutting_pending']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Cutting Pending')); ?></small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 border rounded bg-light">
                                <h4 class="text-success mb-1"><?php echo e($wipStatus['wip_production_completed']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Production Completed')); ?></small>
                            </div>
                        </div>
                        <div class="col-12 mt-3">
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: <?php echo e($wipStatus['wip_completion_rate']); ?>%">
                                    <?php echo e($wipStatus['wip_completion_rate']); ?>% <?php echo e(__('Complete')); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-award me-2"></i><?php echo e(__('Quality Metrics')); ?>

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center p-3 border rounded bg-light">
                                <h4 class="text-success mb-1"><?php echo e($qualityMetrics['quality_pass_rate']); ?>%</h4>
                                <small class="text-muted"><?php echo e(__('Pass Rate')); ?></small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 border rounded bg-light">
                                <h4 class="text-danger mb-1"><?php echo e($qualityMetrics['defect_rate']); ?>%</h4>
                                <small class="text-muted"><?php echo e(__('Defect Rate')); ?></small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 border rounded bg-light">
                                <h4 class="text-warning mb-1"><?php echo e($qualityMetrics['rework_rate']); ?>%</h4>
                                <small class="text-muted"><?php echo e(__('Rework Rate')); ?></small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 border rounded bg-light">
                                <h4 class="text-info mb-1"><?php echo e($qualityMetrics['first_pass_yield']); ?>%</h4>
                                <small class="text-muted"><?php echo e(__('First Pass Yield')); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    <!-- Monthly Production Overview -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-chart-bar me-2"></i><?php echo e(__('Monthly Production Overview')); ?>

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="border rounded p-3 bg-light">
                                <h6 class="text-muted mb-3"><?php echo e(__("This Month's Performance")); ?></h6>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?php echo e(__('Monthly Target')); ?></span>
                                    <span class="fw-semibold text-primary"><?php echo e(number_format($productionTargets['monthly_target'])); ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?php echo e(__('Monthly Actual')); ?></span>
                                    <span class="fw-semibold text-success"><?php echo e(number_format($productionTargets['monthly_actual'])); ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><?php echo e(__('Monthly Efficiency')); ?></span>
                                    <span class="fw-semibold text-info"><?php echo e($productionTargets['monthly_efficiency']); ?>%</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3 bg-light">
                                <h6 class="text-muted mb-3"><?php echo e(__("Weekly Summary")); ?></h6>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?php echo e(__('Weekly Cutting')); ?></span>
                                    <span class="fw-semibold text-info"><?php echo e(number_format($productionStats['weekly_cutting'])); ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><?php echo e(__('Weekly Production')); ?></span>
                                    <span class="fw-semibold text-success"><?php echo e(number_format($productionStats['weekly_production'])); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Production Activities -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0 text-primary">
                        <i class="fas fa-clock me-2"></i><?php echo e(__('Recent Production Activities')); ?>

                    </h6>
                </div>
                <div class="card-body p-3 overflow-auto" style="max-height: 400px;">
                    <?php $__empty_1 = true; $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0 me-3">
                                <div class="rounded-circle d-flex align-items-center justify-content-center" style="width:36px; height:36px; background-color: var(--bs-<?php echo e($activity['color']); ?>); color: white;">
                                    <i class="<?php echo e($activity['icon']); ?>"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1"><?php echo e($activity['message']); ?></p>
                                <small class="text-muted"><?php echo e($activity['time']->diffForHumans()); ?></small>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-info-circle fa-2x mb-2"></i>
                            <p><?php echo e(__('No recent production activities')); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-white border-top p-2">
                    <a href="<?php echo e(route('cuttings.index')); ?>" class="btn btn-outline-primary btn-sm w-100"><?php echo e(__('View Production Details')); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
<style>
/* Production Dashboard Styles */
.welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0.5rem 0 0 0;
}

.welcome-date {
    font-size: 1rem;
    opacity: 0.8;
}

/* Production KPI Cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* Production Line Performance Cards */
.production-line-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.production-line-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* Quality Metrics Styling */
.quality-metric {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: background-color 0.3s ease;
}

.quality-metric:hover {
    background: #e9ecef;
}

/* Progress Bar Styling */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Production Activity Timeline */
.activity-timeline {
    position: relative;
}

.activity-item {
    padding: 0.75rem 0;
    border-left: 2px solid #e9ecef;
    padding-left: 1rem;
    margin-left: 1rem;
    position: relative;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 1rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .welcome-title {
        font-size: 1.5rem;
    }
}


</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function () {
    // Load production dashboard data
    getProductionDashboardData();

    // Auto-refresh production data every 2 minutes
    setInterval(function() {
        getProductionDashboardData();
    }, 120000);

    // Real-time clock update
    function updateClock() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        $('.welcome-date').html('<i class="fas fa-industry me-2"></i>' + now.toLocaleDateString('en-US', options));
    }

    // Update clock every minute
    setInterval(updateClock, 60000);

    // Production Dashboard Data function
    function getProductionDashboardData() {
        var url = $('#get-dashboard').val();
        $.ajax({
            type: "GET",
            url: url,
            dataType: "json",
            success: function (res) {
                // Update production KPI cards with animation
                updateKPICard('today_cutting', res.today_cutting || 0);
                updateKPICard('today_production', res.today_production || 0);
                updateKPICard('weekly_cutting', res.weekly_cutting || 0);
                updateKPICard('weekly_production', res.weekly_production || 0);
                updateKPICard('monthly_cutting', res.monthly_cutting || 0);
                updateKPICard('monthly_production', res.monthly_production || 0);

                // Update efficiency metrics
                updateEfficiencyMetrics(res);

                // Update line performance
                updateLinePerformance(res);

                // Update quality metrics
                updateQualityMetrics(res);

                // Show success indicator
                showUpdateIndicator('success');
            },
            error: function() {
                // Handle error
                showUpdateIndicator('error');
                console.log('Failed to load production data');
            }
        });
    }

    // Helper function to update KPI cards with animation
    function updateKPICard(elementId, value) {
        const element = $('#' + elementId);
        if (element.length) {
            element.fadeOut(200, function() {
                $(this).text(formatNumber(value)).fadeIn(200);
            });
        }
    }

    // Helper function to update efficiency metrics
    function updateEfficiencyMetrics(data) {
        if (data.daily_efficiency !== undefined) {
            $('.daily-efficiency').text(data.daily_efficiency + '%');
        }
        if (data.monthly_efficiency !== undefined) {
            $('.monthly-efficiency').text(data.monthly_efficiency + '%');
        }
    }

    // Helper function to update line performance
    function updateLinePerformance(data) {
        if (data.line_1_efficiency !== undefined) {
            $('.line-1-efficiency').text(data.line_1_efficiency + '%');
        }
        if (data.line_2_efficiency !== undefined) {
            $('.line-2-efficiency').text(data.line_2_efficiency + '%');
        }
        if (data.line_3_efficiency !== undefined) {
            $('.line-3-efficiency').text(data.line_3_efficiency + '%');
        }
        if (data.average_line_efficiency !== undefined) {
            $('.average-line-efficiency').text(data.average_line_efficiency + '%');
        }
    }

    // Helper function to update quality metrics
    function updateQualityMetrics(data) {
        if (data.quality_pass_rate !== undefined) {
            $('.quality-pass-rate').text(data.quality_pass_rate + '%');
        }
        if (data.defect_rate !== undefined) {
            $('.defect-rate').text(data.defect_rate + '%');
        }
        if (data.rework_rate !== undefined) {
            $('.rework-rate').text(data.rework_rate + '%');
        }
        if (data.first_pass_yield !== undefined) {
            $('.first-pass-yield').text(data.first_pass_yield + '%');
        }
    }

    // Helper function to format numbers
    function formatNumber(num) {
        return new Intl.NumberFormat().format(num);
    }

    // Helper function to show update indicator
    function showUpdateIndicator(type) {
        const indicator = $('<div class="update-indicator"></div>');
        const icon = type === 'success' ? 'fas fa-check' : 'fas fa-exclamation-triangle';
        const color = type === 'success' ? '#28a745' : '#dc3545';

        indicator.html(`<i class="${icon}"></i>`);
        indicator.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: color,
            color: 'white',
            padding: '10px',
            borderRadius: '50%',
            zIndex: 9999,
            fontSize: '14px'
        });

        $('body').append(indicator);

        setTimeout(function() {
            indicator.fadeOut(500, function() {
                $(this).remove();
            });
        }, 2000);
    }

    // Initialize tooltips for production metrics
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Add click handlers for production cards
    $('.card').on('click', function() {
        $(this).addClass('shadow-lg').removeClass('shadow-sm');
        setTimeout(() => {
            $(this).removeClass('shadow-lg').addClass('shadow-sm');
        }, 200);
    });
});
</script>

<!-- Hidden Data Routes -->
<input type="hidden" value="<?php echo e(route('dashboard.data')); ?>" id="get-dashboard">
<?php $__env->stopPush(); ?>



<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\red\resources\views/pages/dashboard/index.blade.php ENDPATH**/ ?>